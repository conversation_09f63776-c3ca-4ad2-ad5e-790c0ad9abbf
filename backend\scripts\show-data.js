import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

dotenv.config();

const showData = async () => {
  let connection;
  
  try {
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || 'root',
      database: process.env.DB_NAME || 'dorm_management',
      charset: 'utf8mb4'
    });

    console.log('📋 当前数据库内容:\n');

    // 显示角色数据
    console.log('🔐 角色数据:');
    const [roles] = await connection.execute('SELECT * FROM user_roles ORDER BY id');
    roles.forEach(role => {
      console.log(`   ${role.id}. ${role.role_name} - ${role.role_description || '无描述'}`);
    });

    // 显示用户数据
    console.log('\n👥 用户数据:');
    const [users] = await connection.execute(`
      SELECT u.id, u.name, u.email, ur.role_name, u.phone 
      FROM users u 
      LEFT JOIN user_roles ur ON u.role_id = ur.id 
      ORDER BY u.role_id, u.name
    `);
    users.forEach(user => {
      console.log(`   ${user.name} (${user.email}) - ${user.role_name} - ${user.phone || '无电话'}`);
    });

    // 显示学院数据
    console.log('\n🏫 学院数据:');
    const [colleges] = await connection.execute('SELECT * FROM colleges ORDER BY name');
    colleges.forEach(college => {
      console.log(`   ${college.name} (ID: ${college.id})`);
    });

    // 显示专业数据
    console.log('\n📚 专业数据:');
    const [majors] = await connection.execute(`
      SELECT m.name, c.name as college_name 
      FROM majors m 
      LEFT JOIN colleges c ON m.college_id = c.id 
      ORDER BY c.name, m.name
    `);
    majors.forEach(major => {
      console.log(`   ${major.name} (所属: ${major.college_name})`);
    });

    // 显示宿舍楼数据
    console.log('\n🏢 宿舍楼数据:');
    const [buildings] = await connection.execute(`
      SELECT db.name, db.floors, db.total_rooms, u.name as admin_name
      FROM dorm_buildings db
      LEFT JOIN users u ON db.assigned_admin_id = u.id
      ORDER BY db.name
    `);
    buildings.forEach(building => {
      console.log(`   ${building.name} - ${building.floors}层, ${building.total_rooms}间房 (管理员: ${building.admin_name || '未分配'})`);
    });

    // 显示维修请求数据
    console.log('\n🔧 维修请求数据:');
    const [repairs] = await connection.execute(`
      SELECT rr.description, rr.status, u.name as student_name, rr.room_number, db.name as building_name
      FROM repair_requests rr 
      LEFT JOIN users u ON rr.student_id = u.id 
      LEFT JOIN dorm_buildings db ON rr.dorm_building_id = db.id
      ORDER BY rr.created_at DESC
    `);
    
    if (repairs.length === 0) {
      console.log('   暂无维修请求');
    } else {
      repairs.forEach(repair => {
        console.log(`   ${repair.description} - ${repair.status} (${repair.student_name}, ${repair.building_name}${repair.room_number})`);
      });
    }

    console.log('\n🎯 测试账户 (密码都是: password123):');
    console.log('   📧 系统管理员: <EMAIL>');
    console.log('   📧 宿舍管理员: <EMAIL>');
    console.log('   📧 学生: <EMAIL>');
    console.log('   📧 维修人员: <EMAIL>');

    console.log('\n✅ 数据库已准备就绪，可以开始测试前端功能！');
    
  } catch (error) {
    console.error('❌ 查询数据失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
};

showData();
