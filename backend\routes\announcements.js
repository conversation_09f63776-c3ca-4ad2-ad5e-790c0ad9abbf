import express from 'express';
import { 
  getAnnouncements, 
  createAnnouncement, 
  updateAnnouncement, 
  deleteAnnouncement,
  getAnnouncementById
} from '../controllers/announcementController.js';
import { authenticateToken } from '../middleware/auth.js';

const router = express.Router();

// 获取所有公告
router.get('/', authenticateToken, getAnnouncements);

// 获取单个公告详情
router.get('/:id', authenticateToken, getAnnouncementById);

// 创建公告
router.post('/', authenticateToken, createAnnouncement);

// 更新公告
router.put('/:id', authenticateToken, updateAnnouncement);

// 删除公告
router.delete('/:id', authenticateToken, deleteAnnouncement);

export default router;
