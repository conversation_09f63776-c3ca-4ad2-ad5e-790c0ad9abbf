import React, { useState, FormEvent } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { UserRole } from '../types';
import { APP_NAME } from '../constants';
import Button from '../components/Button';
import Input from '../components/Input';
import Card from '../components/Card';

const LoginPage: React.FC = () => {
  const [isRegistering, setIsRegistering] = useState(false);
  
  // Login states
  const [loginEmail, setLoginEmail] = useState('');
  const [loginPassword, setLoginPassword] = useState('');
  const [loginSelectedRole, setLoginSelectedRole] = useState<UserRole | ''>('');
  
  // Register states
  const [registerName, setRegisterName] = useState('');
  const [registerEmail, setRegisterEmail] = useState('');
  const [registerPassword, setRegisterPassword] = useState('');
  const [registerConfirmPassword, setRegisterConfirmPassword] = useState('');
  const [registerSelectedRole, setRegisterSelectedRole] = useState<UserRole>(UserRole.STUDENT);

  const [error, setError] = useState('');
  const { login, register, isLoading } = useAuth();
  const navigate = useNavigate();

  // 测试账户提示信息
  const testAccountsInfo = {
    '系统管理员': '<EMAIL>',
    '宿舍管理员': '<EMAIL> 或 <EMAIL>',
    '学生': '<EMAIL> 或 <EMAIL>',
    '维修人员': '<EMAIL>'
  };

  const handleLoginSubmit = async (e: FormEvent) => {
    e.preventDefault();
    setError('');
    if (!loginEmail || !loginSelectedRole || !loginPassword) {
      setError('请输入邮箱、密码并选择角色。');
      return;
    }
    try {
      await login(loginEmail, loginSelectedRole, loginPassword);
      navigate('/dashboard');
    } catch (err: any) {
      setError(err.message || '登录失败，请检查您的凭据。');
    }
  };

  const handleRegisterSubmit = async (e: FormEvent) => {
    e.preventDefault();
    setError('');
    if (!registerName || !registerEmail || !registerPassword || !registerConfirmPassword || !registerSelectedRole) {
      setError('请填写所有注册字段。');
      return;
    }
    if (registerPassword !== registerConfirmPassword) {
      setError('两次输入的密码不匹配。');
      return;
    }
    try {
      await register(registerName, registerEmail, registerSelectedRole, registerPassword);
      navigate('/dashboard');
    } catch (err: any) {
      setError(err.message || '注册失败，请稍后再试。');
    }
  };
  
  const toggleForm = () => {
    setIsRegistering(!isRegistering);
    setError('');
    setLoginEmail(''); setLoginPassword(''); setLoginSelectedRole('');
    setRegisterName(''); setRegisterEmail(''); setRegisterPassword(''); setRegisterConfirmPassword(''); setRegisterSelectedRole(UserRole.STUDENT);
  };

  // 移除自动填充密码的功能，用户需要手动输入

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* 动态背景 */}
      <div className="absolute inset-0 gradient-bg-3 animate-pulse-slow"></div>
      
      {/* 装饰性元素 */}
      <div className="absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-white opacity-10 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-white opacity-10 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-white opacity-5 rounded-full blur-3xl"></div>
      </div>

      {/* 主要内容 */}
      <div className="relative z-10 min-h-screen flex items-center justify-center p-4">
        <div className="w-full max-w-md animate-fade-in">
          {/* Logo和标题区域 */}
          <div className="text-center mb-8">
            <div className="inline-flex items-center justify-center w-20 h-20 bg-white bg-opacity-20 rounded-full mb-6 backdrop-blur-sm border border-white border-opacity-30">
              <i className={`fas ${isRegistering ? 'fa-user-plus' : 'fa-university'} fa-3x text-white`}></i>
            </div>
            <h1 className="text-3xl font-bold text-white mb-2 drop-shadow-lg">
              {isRegistering ? '创建账户' : APP_NAME}
            </h1>
            <p className="text-white text-opacity-90 text-lg">
              {isRegistering ? '开始您的智能宿舍管理之旅' : '智能宿舍管理系统'}
            </p>
          </div>

          {/* 登录/注册卡片 */}
          <div className="glass shadow-strong rounded-2xl p-8 animate-slide-in">
            {isRegistering ? (
              // 注册表单
              <form onSubmit={handleRegisterSubmit} className="space-y-6">
                <Input
                  label="姓名"
                  id="register_name"
                  type="text"
                  value={registerName}
                  onChange={(e) => setRegisterName(e.target.value)}
                  placeholder="请输入您的姓名"
                  required
                  containerClassName="bg-white bg-opacity-80 p-3 rounded-xl backdrop-blur-sm"
                  className="input-focus"
                />
                <Input
                  label="邮箱地址"
                  id="register_email"
                  type="email"
                  value={registerEmail}
                  onChange={(e) => setRegisterEmail(e.target.value)}
                  placeholder="请输入您的邮箱"
                  required
                  containerClassName="bg-white bg-opacity-80 p-3 rounded-xl backdrop-blur-sm"
                  className="input-focus"
                />
                <Input
                  label="密码"
                  id="register_password"
                  type="password"
                  value={registerPassword}
                  onChange={(e) => setRegisterPassword(e.target.value)}
                  placeholder="请输入密码 (至少6位)"
                  required
                  minLength={6}
                  containerClassName="bg-white bg-opacity-80 p-3 rounded-xl backdrop-blur-sm"
                  className="input-focus"
                />
                <Input
                  label="确认密码"
                  id="register_confirm_password"
                  type="password"
                  value={registerConfirmPassword}
                  onChange={(e) => setRegisterConfirmPassword(e.target.value)}
                  placeholder="请再次输入密码"
                  required
                  minLength={6}
                  containerClassName="bg-white bg-opacity-80 p-3 rounded-xl backdrop-blur-sm"
                  className="input-focus"
                />
                <div>
                  <label htmlFor="register_role" className="block text-sm font-medium text-gray-700 mb-2">选择注册角色</label>
                  <select
                    id="register_role"
                    value={registerSelectedRole}
                    onChange={(e) => setRegisterSelectedRole(e.target.value as UserRole)}
                    className="block w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white bg-opacity-80 backdrop-blur-sm input-focus"
                    required
                  >
                    <option value={UserRole.STUDENT}>学生</option>
                    <option value={UserRole.REPAIR_STAFF}>维修人员</option>
                  </select>
                </div>

                {error && (
                  <div className="bg-red-50 border border-red-200 rounded-xl p-4 animate-fade-in">
                    <p className="text-sm text-red-600 text-center">{error}</p>
                  </div>
                )}
                
                <Button 
                  type="submit" 
                  className="w-full py-3 text-lg font-semibold btn-hover shadow-medium" 
                  isLoading={isLoading}
                >
                  创建账户
                </Button>
                
                <div className="text-center">
                  <p className="text-gray-600 text-sm">
                    已有账户? 
                    <button 
                      type="button" 
                      onClick={toggleForm} 
                      className="ml-1 font-semibold text-blue-600 hover:text-blue-500 transition-colors duration-200"
                    >
                      立即登录
                    </button>
                  </p>
                </div>
              </form>
            ) : (
              // 登录表单
              <form onSubmit={handleLoginSubmit} className="space-y-6">
                <div>
                  <label htmlFor="login_role" className="block text-sm font-medium text-gray-700 mb-2">选择角色</label>
                  <select
                    id="login_role"
                    value={loginSelectedRole}
                    onChange={(e) => {
                      setLoginSelectedRole(e.target.value as UserRole);
                      setLoginEmail(''); 
                      setLoginPassword(''); 
                    }}
                    className="block w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-white bg-opacity-80 backdrop-blur-sm input-focus"
                    required
                  >
                    <option value="" disabled>-- 请选择角色 --</option>
                    {Object.values(UserRole).map(role => (
                      <option key={role} value={role}>{role}</option>
                    ))}
                  </select>
                </div>

                {loginSelectedRole && (
                  <>
                    <Input
                      label="邮箱地址"
                      id="login_email"
                      type="email"
                      value={loginEmail}
                      onChange={(e) => setLoginEmail(e.target.value)}
                      placeholder={`请输入邮箱地址，如：${testAccountsInfo[loginSelectedRole]}`}
                      required
                      containerClassName="bg-white bg-opacity-80 p-3 rounded-xl backdrop-blur-sm"
                      className="input-focus"
                    />
                    <Input
                      label="密码"
                      id="login_password"
                      type="password"
                      value={loginPassword}
                      onChange={(e) => setLoginPassword(e.target.value)}
                      placeholder="请输入您的密码"
                      required
                      containerClassName="bg-white bg-opacity-80 p-3 rounded-xl backdrop-blur-sm"
                      className="input-focus"
                    />

                    {/* 测试账户提示 */}
                    <div className="bg-blue-50 border border-blue-200 rounded-xl p-4">
                      <div className="flex justify-between items-start mb-2">
                        <h4 className="text-sm font-medium text-blue-800">💡 测试账户提示</h4>
                        <button
                          type="button"
                          onClick={() => {
                            const firstEmail = testAccountsInfo[loginSelectedRole].split(' 或 ')[0];
                            setLoginEmail(firstEmail);
                            setLoginPassword('password123');
                          }}
                          className="text-xs bg-blue-100 hover:bg-blue-200 text-blue-700 px-2 py-1 rounded transition-colors"
                        >
                          快速填充
                        </button>
                      </div>
                      <p className="text-xs text-blue-600 mb-1">
                        <strong>邮箱:</strong> {testAccountsInfo[loginSelectedRole]}
                      </p>
                      <p className="text-xs text-blue-600">
                        <strong>密码:</strong> password123
                      </p>
                    </div>
                  </>
                )}
                
                {error && (
                  <div className="bg-red-50 border border-red-200 rounded-xl p-4 animate-fade-in">
                    <p className="text-sm text-red-600 text-center">{error}</p>
                  </div>
                )}
                
                <Button
                  type="submit"
                  className="w-full py-3 text-lg font-semibold btn-hover shadow-medium"
                  isLoading={isLoading}
                  disabled={!loginEmail || !loginSelectedRole || !loginPassword}
                >
                  登录系统
                </Button>
                
                <div className="text-center">
                  <p className="text-gray-600 text-sm">
                    还没有账户? 
                    <button 
                      type="button" 
                      onClick={toggleForm} 
                      className="ml-1 font-semibold text-blue-600 hover:text-blue-500 transition-colors duration-200"
                    >
                      免费注册
                    </button>
                  </p>
                </div>
              </form>
            )}
          </div>

          {/* 底部信息 */}
          <div className="text-center mt-8">
            <p className="text-white text-opacity-70 text-sm">
              © 2024 {APP_NAME} - 智能宿舍管理系统
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;