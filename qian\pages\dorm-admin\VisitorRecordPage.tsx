
import React, { useState, useEffect, FormEvent } from 'react';
import { Visitor, UserRole } from '../../types';
import { MOCK_USERS, getBuildingIdByName } from '../../constants';
import { useAuth } from '../../contexts/AuthContext';
import Table from '../../components/Table';
import Card from '../../components/Card';
import Button from '../../components/Button';
import Modal from '../../components/Modal';
import Input from '../../components/Input';

const VisitorRecordPage: React.FC = () => {
  const { currentUser } = useAuth();
  const [visitors, setVisitors] = useState<Visitor[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [newVisitor, setNewVisitor] = useState<Partial<Visitor>>({});
  const [editingVisitor, setEditingVisitor] = useState<Visitor | null>(null);
  const [loading, setLoading] = useState(true);
  const [students, setStudents] = useState<any[]>([]);

  if (!currentUser || currentUser.role !== UserRole.DORM_ADMIN) return <p>权限不足。</p>;

  // 直接使用宿舍楼ID，因为张三管理A栋
  const adminBuildingId = currentUser.id === 'dormadmin01' ? 'bldgA' :
                         currentUser.id === 'dormadmin02' ? 'bldgB' :
                         getBuildingIdByName(currentUser.dormBuilding);
  const buildingName = currentUser.id === 'dormadmin01' ? 'A栋' :
                      currentUser.id === 'dormadmin02' ? 'B栋' :
                      currentUser.dormBuilding || "未知楼栋";

  // 获取访客记录数据
  const fetchVisitors = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      const response = await fetch(`http://localhost:3002/api/visitors?building_id=${adminBuildingId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          const formattedData = data.data.map((v: any) => ({
            id: v.id,
            visitorName: v.visitor_name,
            visitorIdNumber: v.visitor_id_number,
            reason: v.reason,
            entryTime: v.entry_time,
            exitTime: v.exit_time,
            visitedStudentId: v.visited_student_id,
            visitedStudentName: v.visited_student_name,
            dormBuildingId: v.dorm_building_id,
            recordedBy: v.recorded_by
          }));
          setVisitors(formattedData);
        }
      } else {
        console.error('获取访客记录失败');
      }
    } catch (error) {
      console.error('获取访客记录错误:', error);
    } finally {
      setLoading(false);
    }
  };

  // 获取学生列表
  const fetchStudents = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`http://localhost:3002/api/student-allocation/students?building_id=${adminBuildingId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setStudents(data.data);
        }
      }
    } catch (error) {
      console.error('获取学生列表错误:', error);
    }
  };

  useEffect(() => {
    fetchVisitors();
    fetchStudents();
  }, [adminBuildingId]);

  const columns = [
    { header: '访客姓名', accessor: 'visitorName' as keyof Visitor },
    { header: '证件号', accessor: 'visitorIdNumber' as keyof Visitor },
    { header: '被访学生', accessor: 'visitedStudentName' as keyof Visitor },
    { header: '进入时间', accessor: (v: Visitor) => new Date(v.entryTime).toLocaleString() },
    { header: '离开时间', accessor: (v: Visitor) => v.exitTime ? new Date(v.exitTime).toLocaleString() : '未离开' },
    { 
      header: '操作', 
      accessor: 'id' as keyof Visitor,
      render: (visitor: Visitor) => (
        <div className="space-x-1">
          <Button size="sm" variant="ghost" onClick={() => handleEdit(visitor)}><i className="fas fa-edit"></i> {visitor.exitTime ? '' : '登记离开'}</Button>
          <Button size="sm" variant="danger" onClick={() => handleDelete(visitor.id)}><i className="fas fa-trash"></i></Button>
        </div>
      )
    }
  ];
  
  const handleEdit = (visitor: Visitor) => {
    setEditingVisitor(visitor);
    setNewVisitor({...visitor, entryTime: visitor.entryTime.split('T')[0] + 'T' + new Date(visitor.entryTime).toLocaleTimeString('sv-SE', { hour: '2-digit', minute: '2-digit'}) }); // Format for datetime-local
    setIsModalOpen(true);
  };
  
  const handleDelete = (id: string) => {
    if (window.confirm("确定删除此访客记录吗?")) {
        setVisitors(visitors.filter(v => v.id !== id));
    }
  };

  const handleOpenModal = () => {
    setEditingVisitor(null);
    setNewVisitor({ 
        recordedBy: currentUser.id, 
        dormBuildingId: adminBuildingId, 
        entryTime: new Date().toISOString().substring(0, 16) // For datetime-local
    });
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setNewVisitor({});
    setEditingVisitor(null);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    if (name === "visitedStudentId") {
        const student = studentsInAdminBuilding.find(s => s.id === value);
        setNewVisitor(prev => ({ ...prev, visitedStudentId: value, visitedStudentName: student?.name }));
    } else {
        setNewVisitor(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (editingVisitor) {
        if(newVisitor.exitTime){ // Ensure exitTime is later than entryTime
            if(new Date(newVisitor.exitTime) < new Date(editingVisitor.entryTime)){
                alert("离开时间不能早于进入时间。");
                return;
            }
        }
        setVisitors(visitors.map(v => v.id === editingVisitor.id ? {...newVisitor, entryTime: new Date(newVisitor.entryTime!).toISOString(), exitTime: newVisitor.exitTime ? new Date(newVisitor.exitTime).toISOString() : undefined } as Visitor : v));

    } else {
        if (newVisitor.visitorName && newVisitor.visitedStudentId && newVisitor.entryTime) {
            setVisitors([...visitors, { ...newVisitor, id: `vis_${Date.now()}`, entryTime: new Date(newVisitor.entryTime).toISOString() } as Visitor]);
        } else {
            alert("请填写所有必填项。");
            return;
        }
    }
    handleCloseModal();
  };

  return (
    <Card title={`${buildingName} - 访客登记管理`} actions={<Button onClick={handleOpenModal} leftIcon={<i className="fas fa-id-card mr-2"></i>}>登记访客</Button>}>
      <Table columns={columns} data={filteredVisitors} keyExtractor={v => v.id} />
      <Modal isOpen={isModalOpen} onClose={handleCloseModal} title={editingVisitor ? "编辑/登记离开" : "登记新访客"}>
        <form onSubmit={handleSubmit} className="space-y-4">
          <Input name="visitorName" label="访客姓名" value={newVisitor.visitorName || ''} onChange={handleInputChange} required />
          <Input name="visitorIdNumber" label="访客证件号" value={newVisitor.visitorIdNumber || ''} onChange={handleInputChange} />
          <div>
            <label htmlFor="visitedStudentId" className="block text-sm font-medium text-gray-700 mb-1">被访学生</label>
            <select
              id="visitedStudentId"
              name="visitedStudentId"
              value={newVisitor.visitedStudentId || ''}
              onChange={handleInputChange}
              className="block w-full px-3 py-2 border border-neutral-DEFAULT rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
              required
            >
              <option value="">-- 选择学生 --</option>
              {studentsInAdminBuilding.map(s => <option key={s.id} value={s.id}>{s.name} ({s.roomNumber})</option>)}
            </select>
          </div>
          <Input name="reason" label="来访事由" value={newVisitor.reason || ''} onChange={handleInputChange} />
          <Input name="entryTime" label="进入时间" type="datetime-local" value={newVisitor.entryTime || ''} onChange={handleInputChange} required />
          { (editingVisitor || newVisitor.entryTime) && 
            <Input name="exitTime" label="离开时间 (可选)" type="datetime-local" value={newVisitor.exitTime || ''} onChange={handleInputChange} />
          }
          <div className="flex justify-end space-x-2 pt-4">
            <Button type="button" variant="ghost" onClick={handleCloseModal}>取消</Button>
            <Button type="submit">{editingVisitor ? "保存更改" : "登记访客"}</Button>
          </div>
        </form>
      </Modal>
    </Card>
  );
};

export default VisitorRecordPage;
