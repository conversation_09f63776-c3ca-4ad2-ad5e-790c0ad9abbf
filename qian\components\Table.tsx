import React, { ReactNode } from 'react';

interface TableColumn<T> {
  header: string;
  accessor: keyof T | ((item: T) => ReactNode);
  render?: (item: T) => ReactNode;
  className?: string;
  width?: string;
}

interface TableProps<T> {
  columns: TableColumn<T>[];
  data: T[];
  keyExtractor: (item: T) => string | number;
  onRowClick?: (item: T) => void;
  isLoading?: boolean;
  emptyStateMessage?: string;
  striped?: boolean;
  hover?: boolean;
}

const Table = <T extends object,>({ 
  columns, 
  data, 
  keyExtractor, 
  onRowClick, 
  isLoading = false,
  emptyStateMessage = "暂无数据",
  striped = true,
  hover = true
}: TableProps<T>): React.ReactElement => {
  return (
    <div className="overflow-hidden bg-white shadow-medium rounded-2xl border border-gray-100">
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gradient-to-r from-gray-50 to-gray-100">
            <tr>
              {columns.map((col, index) => (
                <th
                  key={index}
                  scope="col"
                  style={{ width: col.width }}
                  className={`px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider ${col.className || ''}`}
                >
                  <div className="flex items-center">
                    <span className="w-1 h-4 bg-gradient-to-b from-blue-500 to-purple-600 rounded-full mr-3"></span>
                    {col.header}
                  </div>
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-100">
            {isLoading ? (
              <tr>
                <td colSpan={columns.length} className="px-6 py-12 text-center">
                  <div className="flex flex-col items-center justify-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mb-3"></div>
                    <p className="text-gray-500 text-sm">加载中...</p>
                  </div>
                </td>
              </tr>
            ) : data.length === 0 ? (
              <tr>
                <td colSpan={columns.length} className="px-6 py-12 text-center">
                  <div className="flex flex-col items-center justify-center">
                    <i className="fas fa-inbox text-4xl text-gray-300 mb-3"></i>
                    <p className="text-gray-500 text-sm">{emptyStateMessage}</p>
                  </div>
                </td>
              </tr>
            ) : (
              data.map((item, rowIndex) => (
                <tr 
                  key={keyExtractor(item)} 
                  className={`
                    ${onRowClick ? 'cursor-pointer' : ''} 
                    ${hover ? 'hover:bg-blue-50 hover:shadow-sm' : ''} 
                    ${striped && rowIndex % 2 === 1 ? 'bg-gray-50' : ''}
                    transition-all duration-200 ease-in-out
                  `}
                  onClick={() => onRowClick && onRowClick(item)}
                >
                  {columns.map((col, index) => (
                    <td 
                      key={index} 
                      className={`px-6 py-4 whitespace-nowrap text-sm text-gray-700 ${col.className || ''}`}
                    >
                      <div className="flex items-center">
                        {col.render 
                          ? col.render(item) 
                          : typeof col.accessor === 'function' 
                            ? col.accessor(item) 
                            : String(item[col.accessor] ?? '')}
                      </div>
                    </td>
                  ))}
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default Table;