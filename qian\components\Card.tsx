import React, { ReactNode } from 'react';

interface CardProps {
  title?: string;
  children: ReactNode;
  className?: string;
  titleClassName?: string;
  bodyClassName?: string;
  actions?: ReactNode; // Optional slot for action buttons or elements
  hover?: boolean;
  glass?: boolean;
}

const Card: React.FC<CardProps> = ({ 
  title, 
  children, 
  className = '', 
  titleClassName = '', 
  bodyClassName = '', 
  actions,
  hover = false,
  glass = false
}) => {
  const baseClasses = glass 
    ? 'glass shadow-strong' 
    : 'bg-white shadow-medium';
  
  const hoverClasses = hover ? 'card-hover' : '';
  
  return (
    <div className={`${baseClasses} rounded-2xl overflow-hidden ${hoverClasses} ${className}`}>
      {title && (
        <div className={`px-6 py-5 border-b border-gray-100 flex justify-between items-center bg-gradient-to-r from-gray-50 to-white ${titleClassName}`}>
          <h2 className="text-xl font-bold text-gray-800 flex items-center">
            <span className="w-1 h-6 bg-gradient-to-b from-blue-500 to-purple-600 rounded-full mr-3"></span>
            {title}
          </h2>
          {actions && <div className="flex items-center space-x-2">{actions}</div>}
        </div>
      )}
      <div className={`p-6 ${bodyClassName}`}>
        {children}
      </div>
    </div>
  );
};

export default Card;
    