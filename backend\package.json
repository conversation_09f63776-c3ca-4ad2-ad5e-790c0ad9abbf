{"name": "dorm-management-backend", "version": "1.0.0", "description": "易宿管 - 智能宿舍管理系统后端API", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "init-db": "node scripts/init-database.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["dorm", "management", "api", "express", "mysql"], "author": "", "license": "MIT", "dependencies": {"express": "^4.18.2", "mysql2": "^3.6.5", "bcrypt": "^5.1.1", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5"}, "devDependencies": {"nodemon": "^3.0.2"}}