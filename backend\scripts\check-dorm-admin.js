import mysql from 'mysql2/promise';

const checkDormAdmin = async () => {
  let connection;
  
  try {
    connection = await mysql.createConnection({
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: 'root',
      database: 'dorm_management',
      charset: 'utf8mb4'
    });

    console.log('🔍 检查宿管张三的信息...\n');

    // 查找张三的详细信息
    const [zhangsan] = await connection.execute(`
      SELECT u.*, ur.role_name, db.name as building_name
      FROM users u 
      LEFT JOIN user_roles ur ON u.role_id = ur.id
      LEFT JOIN dorm_buildings db ON u.dorm_building_id = db.id
      WHERE u.name = '张三'
    `);

    if (zhangsan.length === 0) {
      console.log('❌ 没有找到张三');
      return;
    }

    const user = zhangsan[0];
    console.log('👤 张三的信息:');
    console.log(`   ID: ${user.id}`);
    console.log(`   姓名: ${user.name}`);
    console.log(`   邮箱: ${user.email}`);
    console.log(`   角色: ${user.role_name}`);
    console.log(`   分配的宿舍楼: ${user.building_name || '未分配'}`);
    console.log(`   dorm_building_id: ${user.dorm_building_id || '无'}`);

    // 查看所有宿舍楼信息
    console.log('\n🏢 所有宿舍楼信息:');
    const [buildings] = await connection.execute(`
      SELECT db.*, u.name as admin_name
      FROM dorm_buildings db
      LEFT JOIN users u ON db.assigned_admin_id = u.id
      ORDER BY db.created_at
    `);

    buildings.forEach((building, index) => {
      console.log(`   ${index + 1}. ${building.name} (ID: ${building.id})`);
      console.log(`      楼层: ${building.floors}, 房间: ${building.total_rooms}`);
      console.log(`      分配管理员: ${building.admin_name || '未分配'} (ID: ${building.assigned_admin_id || '无'})`);
      console.log('');
    });

    // 检查是否有房间表
    console.log('🚪 检查房间表:');
    try {
      const [rooms] = await connection.execute('SELECT COUNT(*) as count FROM rooms');
      console.log(`   房间总数: ${rooms[0].count}`);
      
      if (rooms[0].count > 0) {
        const [sampleRooms] = await connection.execute(`
          SELECT r.*, db.name as building_name
          FROM rooms r
          LEFT JOIN dorm_buildings db ON r.dorm_building_id = db.id
          LIMIT 5
        `);
        
        console.log('   示例房间:');
        sampleRooms.forEach(room => {
          console.log(`     - ${room.building_name} ${room.room_number} (容量: ${room.capacity})`);
        });
      }
    } catch (error) {
      console.log('   ❌ rooms 表不存在');
    }

    // 检查张三管理的宿舍楼是否有房间
    if (user.dorm_building_id) {
      try {
        const [buildingRooms] = await connection.execute(`
          SELECT COUNT(*) as count FROM rooms WHERE dorm_building_id = ?
        `, [user.dorm_building_id]);
        
        console.log(`\n🏠 张三管理的${user.building_name}的房间数: ${buildingRooms[0].count}`);
      } catch (error) {
        console.log(`\n❌ 无法查询${user.building_name}的房间信息`);
      }
    }

  } catch (error) {
    console.error('❌ 检查失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
};

checkDormAdmin();
