
import React from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { MOCK_ROOMS, MOCK_BEDS, MOCK_DORM_BUILDINGS, MOCK_ROOM_IMAGE_URL, getBuildingIdByName } from '../../constants';
import Card from '../../components/Card';

const MyInfoPage: React.FC = () => {
  const { currentUser } = useAuth();

  if (!currentUser) {
    return <p>正在加载用户信息...</p>;
  }

  const { name, email, phone, college, major, dormBuilding: studentDormBuildingName, roomNumber: studentRoomNumber, emergencyContactName, emergencyContactPhone } = currentUser;

  const studentBuildingId = getBuildingIdByName(studentDormBuildingName);
  const studentRoom = MOCK_ROOMS.find(room => room.dormBuildingId === studentBuildingId && room.roomNumber === studentRoomNumber);
  const studentBed = MOCK_BEDS.find(bed => bed.studentId === currentUser.id && bed.roomId === studentRoom?.id);

  return (
    <div className="space-y-6">
      <h1 className="text-3xl font-semibold text-neutral-dark">我的信息</h1>

      <Card title="个人资料">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div><span className="font-semibold">姓名:</span> {name}</div>
          <div><span className="font-semibold">邮箱:</span> {email}</div>
          <div><span className="font-semibold">我的电话:</span> {phone || '未填写'}</div>
          <div><span className="font-semibold">学院:</span> {college || '未分配'}</div>
          <div><span className="font-semibold">专业:</span> {major || '未分配'}</div>
          <div><span className="font-semibold">第一紧急联系人:</span> {emergencyContactName || '未填写'}</div>
          <div><span className="font-semibold">联系人电话:</span> {emergencyContactPhone || '未填写'}</div>
        </div>
      </Card>

      <Card title="我的宿舍信息">
        {studentRoom && studentBed ? (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 items-start">
            <div>
              <p><span className="font-semibold">宿舍楼:</span> {studentDormBuildingName}</p>
              <p><span className="font-semibold">房间号:</span> {studentRoom.roomNumber}</p>
              <p><span className="font-semibold">楼层:</span> {studentRoom.floor}</p>
              <p><span className="font-semibold">房间类型:</span> {studentRoom.type}</p>
              <p><span className="font-semibold">房间容量:</span> {studentRoom.capacity}人</p>
              <p><span className="font-semibold">当前已住:</span> {studentRoom.occupiedBeds}人</p>
              <hr className="my-3"/>
              <p><span className="font-semibold">我的床位号:</span> {studentBed.bedNumber}</p>
              <p><span className="font-semibold">床位状态:</span> {studentBed.status}</p>
            </div>
            <div>
              <img 
                src={`${MOCK_ROOM_IMAGE_URL}&${studentRoom.id}`} // Add room ID to vary image
                alt={`${studentRoom.roomNumber} 房间图片`} 
                className="rounded-lg shadow-md w-full h-auto object-cover max-h-80"
              />
              <p className="text-xs text-center text-gray-500 mt-2">房间图片 (模拟)</p>
            </div>
          </div>
        ) : (
          <p>您目前尚未分配宿舍或床位信息不完整。</p>
        )}
      </Card>
    </div>
  );
};

export default MyInfoPage;