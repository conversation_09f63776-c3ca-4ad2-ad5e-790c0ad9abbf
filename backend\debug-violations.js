import { pool } from './config/database.js';

async function checkViolationData() {
  try {
    console.log('=== 检查违规记录数据 ===');
    const [violations] = await pool.execute('SELECT * FROM violations');
    console.log('违规记录数量:', violations.length);
    violations.forEach((v, i) => {
      console.log(`违规记录 ${i+1}:`, {
        id: v.id,
        student_id: v.student_id,
        type: v.type,
        description: v.description
      });
    });

    console.log('\n=== 检查所有用户数据 ===');
    const [users] = await pool.execute('SELECT id, name, email, role_name FROM users');
    console.log('用户数量:', users.length);
    users.forEach((u, i) => {
      console.log(`用户 ${i+1}:`, {
        id: u.id,
        name: u.name,
        role: u.role_name
      });
    });

    console.log('\n=== 检查关联查询 ===');
    const [joined] = await pool.execute(\`
      SELECT v.id as violation_id, v.student_id, v.type, v.description,
             u.id as user_id, u.name as student_name, u.room_number,
             db.name as building_name
      FROM violations v
      LEFT JOIN users u ON v.student_id = u.id
      LEFT JOIN dorm_buildings db ON v.dorm_building_id = db.id
    \`);
    console.log('关联查询结果数量:', joined.length);
    joined.forEach((j, i) => {
      console.log(\`关联结果 \${i+1}:\`, {
        violation_id: j.violation_id,
        student_id: j.student_id,
        user_id: j.user_id,
        student_name: j.student_name,
        type: j.type
      });
    });

    await pool.end();
  } catch (error) {
    console.error('错误:', error);
    process.exit(1);
  }
}

checkViolationData();
