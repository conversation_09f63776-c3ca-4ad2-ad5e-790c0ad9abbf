import { pool } from './config/database.js';

async function fixBuildingAssignment() {
  try {
    console.log('🔧 修复宿舍楼分配...');
    
    // 更新宿舍楼分配
    await pool.execute(`
      UPDATE dorm_buildings 
      SET assigned_admin_id = 'dormadmin01' 
      WHERE id = 'bldgA'
    `);
    
    await pool.execute(`
      UPDATE dorm_buildings 
      SET assigned_admin_id = 'dormadmin02' 
      WHERE id = 'bldgB'
    `);
    
    console.log('✅ 宿舍楼分配已更新');
    
    // 验证结果
    console.log('\n📊 验证结果:');
    const [buildings] = await pool.execute('SELECT id, name, assigned_admin_id FROM dorm_buildings');
    buildings.forEach(b => {
      console.log(`宿舍楼: ${b.name} (${b.id}) - 管理员: ${b.assigned_admin_id || '未分配'}`);
    });
    
    // 检查张三的信息
    console.log('\n👤 检查张三信息:');
    const [zhangsan] = await pool.execute('SELECT id, name, email FROM users WHERE email = "<EMAIL>"');
    if (zhangsan.length > 0) {
      console.log(`张三ID: ${zhangsan[0].id}, 姓名: ${zhangsan[0].name}`);
    }
    
    // 检查A栋学生
    console.log('\n👥 检查A栋学生:');
    const [students] = await pool.execute(`
      SELECT id, name, dorm_building_id, room_number 
      FROM users 
      WHERE role_id = 3 AND dorm_building_id = 'bldgA'
    `);
    console.log(`A栋学生数量: ${students.length}`);
    students.forEach(s => {
      console.log(`  - ${s.name} (${s.id}) - 房间: ${s.room_number || '未分配'}`);
    });
    
    await pool.end();
    console.log('\n🎉 修复完成！张三现在应该能看到学生分配数据了！');
  } catch (error) {
    console.error('❌ 修复失败:', error);
    process.exit(1);
  }
}

fixBuildingAssignment();
