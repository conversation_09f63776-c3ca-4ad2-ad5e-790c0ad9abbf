import mysql from 'mysql2/promise';

const quickCheck = async () => {
  let connection;
  
  try {
    connection = await mysql.createConnection({
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: 'root',
      database: 'dorm_management',
      charset: 'utf8mb4'
    });

    console.log('🔍 快速检查数据库状态...\n');

    // 检查所有宿舍管理员
    console.log('👥 所有宿舍管理员:');
    const [admins] = await connection.execute(`
      SELECT u.id, u.name, u.email, u.dorm_building_id, db.name as building_name
      FROM users u 
      LEFT JOIN user_roles ur ON u.role_id = ur.id
      LEFT JOIN dorm_buildings db ON u.dorm_building_id = db.id
      WHERE ur.role_name = '宿舍管理员'
    `);

    admins.forEach((admin, index) => {
      console.log(`   ${index + 1}. ${admin.name} (${admin.email})`);
      console.log(`      ID: ${admin.id}`);
      console.log(`      分配宿舍楼: ${admin.building_name || '未分配'} (ID: ${admin.dorm_building_id || '无'})`);
      console.log('');
    });

    // 检查所有宿舍楼
    console.log('🏢 所有宿舍楼:');
    const [buildings] = await connection.execute(`
      SELECT db.*, u.name as admin_name, u.email as admin_email
      FROM dorm_buildings db
      LEFT JOIN users u ON db.assigned_admin_id = u.id
    `);

    buildings.forEach((building, index) => {
      console.log(`   ${index + 1}. ${building.name} (ID: ${building.id})`);
      console.log(`      分配管理员: ${building.admin_name || '未分配'} (${building.admin_email || ''})`);
      console.log(`      管理员ID: ${building.assigned_admin_id || '无'}`);
      console.log('');
    });

    // 检查房间数量
    console.log('🏠 房间统计:');
    const [roomStats] = await connection.execute(`
      SELECT 
        db.name as building_name,
        COUNT(r.id) as room_count
      FROM dorm_buildings db
      LEFT JOIN rooms r ON db.id = r.dorm_building_id
      GROUP BY db.id, db.name
    `);

    roomStats.forEach((stat, index) => {
      console.log(`   ${index + 1}. ${stat.building_name}: ${stat.room_count} 个房间`);
    });

  } catch (error) {
    console.error('❌ 检查失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
};

quickCheck();
