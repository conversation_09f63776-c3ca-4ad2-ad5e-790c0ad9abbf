import mysql from 'mysql2/promise';

const checkCurrentData = async () => {
  let connection;
  
  try {
    connection = await mysql.createConnection({
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: 'root',
      database: 'dorm_management',
      charset: 'utf8mb4'
    });

    console.log('📋 当前数据库内容:\n');

    // 检查用户数据
    console.log('👥 用户数据:');
    const [users] = await connection.execute(`
      SELECT u.id, u.name, u.email, ur.role_name, u.phone 
      FROM users u 
      LEFT JOIN user_roles ur ON u.role_id = ur.id 
      ORDER BY u.created_at DESC
    `);
    
    if (users.length === 0) {
      console.log('   ❌ 没有用户数据');
    } else {
      users.forEach((user, index) => {
        console.log(`   ${index + 1}. ${user.name} (${user.email}) - ${user.role_name || '未知角色'}`);
      });
    }

    // 检查学院数据
    console.log('\n🏫 学院数据:');
    const [colleges] = await connection.execute('SELECT * FROM colleges ORDER BY created_at DESC');
    
    if (colleges.length === 0) {
      console.log('   ❌ 没有学院数据');
    } else {
      colleges.forEach((college, index) => {
        console.log(`   ${index + 1}. ${college.name} (ID: ${college.id})`);
      });
    }

    // 检查专业数据
    console.log('\n📚 专业数据:');
    const [majors] = await connection.execute(`
      SELECT m.name, c.name as college_name 
      FROM majors m 
      LEFT JOIN colleges c ON m.college_id = c.id 
      ORDER BY m.created_at DESC
    `);
    
    if (majors.length === 0) {
      console.log('   ❌ 没有专业数据');
    } else {
      majors.forEach((major, index) => {
        console.log(`   ${index + 1}. ${major.name} (所属: ${major.college_name || '未知学院'})`);
      });
    }

    // 检查宿舍楼数据
    console.log('\n🏢 宿舍楼数据:');
    const [buildings] = await connection.execute(`
      SELECT db.name, db.floors, db.total_rooms, u.name as admin_name
      FROM dorm_buildings db
      LEFT JOIN users u ON db.assigned_admin_id = u.id
      ORDER BY db.created_at DESC
    `);
    
    if (buildings.length === 0) {
      console.log('   ❌ 没有宿舍楼数据');
    } else {
      buildings.forEach((building, index) => {
        console.log(`   ${index + 1}. ${building.name} - ${building.floors}层, ${building.total_rooms}间房 (管理员: ${building.admin_name || '未分配'})`);
      });
    }

    // 检查维修请求数据
    console.log('\n🔧 维修请求数据:');
    const [repairs] = await connection.execute(`
      SELECT rr.description, rr.status, u.name as student_name, rr.room_number, db.name as building_name
      FROM repair_requests rr 
      LEFT JOIN users u ON rr.student_id = u.id 
      LEFT JOIN dorm_buildings db ON rr.dorm_building_id = db.id
      ORDER BY rr.created_at DESC
    `);
    
    if (repairs.length === 0) {
      console.log('   ❌ 没有维修请求数据');
    } else {
      repairs.forEach((repair, index) => {
        console.log(`   ${index + 1}. ${repair.description} - ${repair.status} (${repair.student_name}, ${repair.building_name}${repair.room_number})`);
      });
    }

    // 统计信息
    console.log('\n📊 数据统计:');
    console.log(`   - 用户总数: ${users.length}`);
    console.log(`   - 学院总数: ${colleges.length}`);
    console.log(`   - 专业总数: ${majors.length}`);
    console.log(`   - 宿舍楼总数: ${buildings.length}`);
    console.log(`   - 维修请求总数: ${repairs.length}`);

  } catch (error) {
    console.error('❌ 检查数据失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
};

checkCurrentData();
