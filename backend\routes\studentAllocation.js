import express from 'express';
import { 
  getStudentsInBuilding,
  getVacantBedsInBuilding,
  assignStudentToBed,
  unassignStudent,
  updateStudentEmergencyContact
} from '../controllers/studentAllocationController.js';
import { authenticateToken } from '../middleware/auth.js';

const router = express.Router();

// 获取宿舍楼内的学生列表
router.get('/students', authenticateToken, getStudentsInBuilding);

// 获取宿舍楼内的空闲床位
router.get('/vacant-beds', authenticateToken, getVacantBedsInBuilding);

// 分配学生到床位
router.post('/assign', authenticateToken, assignStudentToBed);

// 取消学生床位分配
router.delete('/unassign/:student_id', authenticateToken, unassignStudent);

// 更新学生紧急联系人信息
router.put('/emergency-contact/:student_id', authenticateToken, updateStudentEmergencyContact);

export default router;
