import mysql from 'mysql2/promise';

const createAnnouncementsTable = async () => {
  let connection;
  
  try {
    connection = await mysql.createConnection({
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: 'root',
      database: 'dorm_management',
      charset: 'utf8mb4'
    });

    console.log('🔄 创建公告表...');

    // 创建公告表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS announcements (
        id VARCHAR(50) PRIMARY KEY,
        title VARCHAR(200) NOT NULL,
        content TEXT NOT NULL,
        author_id VARCHAR(50) NOT NULL,
        scope ENUM('All', 'DormBuilding', 'College') DEFAULT 'All',
        target_id VARCHAR(50),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (author_id) REFERENCES users(id) ON DELETE CASCADE
      )
    `);

    console.log('✅ 公告表创建成功');

    // 插入测试数据
    console.log('🔄 插入测试公告数据...');

    await connection.execute(`
      INSERT IGNORE INTO announcements (id, title, content, author_id, scope, target_id) VALUES
      ('anno001', '近期维修安排通知', '请注意，A栋将于7月25日上午9点至下午5点进行计划内维修。维修期间可能会影响正常用水用电，请提前做好准备。如有紧急情况，请联系宿舍管理员。', 'sysadmin01', 'All', NULL),
      ('anno002', '宿舍会议 - B栋', 'B栋全体住宿学生务必参加7月26日晚上7点在公共活动室举行的会议。会议将讨论宿舍管理规定和安全事项，请准时参加。', 'dormadmin02', 'DormBuilding', 'bldgB'),
      ('anno003', '计算机科学学院通知', '计算机科学学院学生请注意：下周将进行宿舍安全检查，请保持宿舍整洁。', 'sysadmin01', 'College', 'college01')
    `);

    console.log('✅ 测试数据插入成功');

    // 验证数据
    const [count] = await connection.execute('SELECT COUNT(*) as count FROM announcements');
    console.log(`📊 公告数量: ${count[0].count}`);

    const [announcements] = await connection.execute(`
      SELECT a.title, u.name as author_name, a.scope, a.created_at
      FROM announcements a 
      LEFT JOIN users u ON a.author_id = u.id 
      ORDER BY a.created_at DESC
    `);

    console.log('\n📋 现有公告:');
    announcements.forEach((ann, index) => {
      console.log(`   ${index + 1}. ${ann.title} - ${ann.author_name} (${ann.scope})`);
    });

    console.log('\n🎉 公告表设置完成！');

  } catch (error) {
    console.error('❌ 创建公告表失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
};

createAnnouncementsTable();
