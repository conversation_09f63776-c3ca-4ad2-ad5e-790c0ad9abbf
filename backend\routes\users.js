import express from 'express';
import { getUsers, createUser, updateUser, deleteUser } from '../controllers/userController.js';
import { authenticateToken, requireRole } from '../middleware/auth.js';

const router = express.Router();

// 所有用户路由都需要认证
router.use(authenticateToken);

// GET /api/users - 获取用户列表（仅系统管理员）
router.get('/', requireRole(['系统管理员']), getUsers);

// POST /api/users - 创建用户（仅系统管理员）
router.post('/', requireRole(['系统管理员']), createUser);

// PUT /api/users/:id - 更新用户（仅系统管理员）
router.put('/:id', requireRole(['系统管理员']), updateUser);

// DELETE /api/users/:id - 删除用户（仅系统管理员）
router.delete('/:id', requireRole(['系统管理员']), deleteUser);

export default router;
