import { pool } from '../config/database.js';

// 获取专业列表
export const getMajors = async (req, res) => {
  try {
    const [rows] = await pool.execute(
      `SELECT m.id, m.name, m.college_id, c.name as college_name, m.created_at 
       FROM majors m 
       LEFT JOIN colleges c ON m.college_id = c.id 
       ORDER BY c.name, m.name`
    );

    res.json({
      success: true,
      data: {
        majors: rows
      }
    });

  } catch (error) {
    console.error('获取专业列表错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};

// 创建专业
export const createMajor = async (req, res) => {
  try {
    const { name, collegeId } = req.body;

    if (!name || !collegeId) {
      return res.status(400).json({
        success: false,
        message: '专业名称和所属学院都是必填项'
      });
    }

    // 检查学院是否存在
    const [collegeRows] = await pool.execute(
      'SELECT id FROM colleges WHERE id = ?',
      [collegeId]
    );

    if (collegeRows.length === 0) {
      return res.status(400).json({
        success: false,
        message: '指定的学院不存在'
      });
    }

    // 检查同一学院下专业名称是否已存在
    const [existingMajors] = await pool.execute(
      'SELECT id FROM majors WHERE name = ? AND college_id = ?',
      [name, collegeId]
    );

    if (existingMajors.length > 0) {
      return res.status(400).json({
        success: false,
        message: '该学院下已存在同名专业'
      });
    }

    // 生成专业ID
    const majorId = `major_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // 插入新专业
    await pool.execute(
      'INSERT INTO majors (id, name, college_id) VALUES (?, ?, ?)',
      [majorId, name, collegeId]
    );

    res.status(201).json({
      success: true,
      message: '专业创建成功',
      data: {
        majorId
      }
    });

  } catch (error) {
    console.error('创建专业错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};

// 更新专业
export const updateMajor = async (req, res) => {
  try {
    const { id } = req.params;
    const { name, collegeId } = req.body;

    if (!name || !collegeId) {
      return res.status(400).json({
        success: false,
        message: '专业名称和所属学院都是必填项'
      });
    }

    // 检查专业是否存在
    const [existingMajors] = await pool.execute(
      'SELECT id FROM majors WHERE id = ?',
      [id]
    );

    if (existingMajors.length === 0) {
      return res.status(404).json({
        success: false,
        message: '专业不存在'
      });
    }

    // 检查学院是否存在
    const [collegeRows] = await pool.execute(
      'SELECT id FROM colleges WHERE id = ?',
      [collegeId]
    );

    if (collegeRows.length === 0) {
      return res.status(400).json({
        success: false,
        message: '指定的学院不存在'
      });
    }

    // 检查新名称在同一学院下是否与其他专业重复
    const [duplicateMajors] = await pool.execute(
      'SELECT id FROM majors WHERE name = ? AND college_id = ? AND id != ?',
      [name, collegeId, id]
    );

    if (duplicateMajors.length > 0) {
      return res.status(400).json({
        success: false,
        message: '该学院下已存在同名专业'
      });
    }

    // 更新专业信息
    await pool.execute(
      'UPDATE majors SET name = ?, college_id = ? WHERE id = ?',
      [name, collegeId, id]
    );

    res.json({
      success: true,
      message: '专业更新成功'
    });

  } catch (error) {
    console.error('更新专业错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};

// 删除专业
export const deleteMajor = async (req, res) => {
  try {
    const { id } = req.params;

    // 检查专业是否存在
    const [existingMajors] = await pool.execute(
      'SELECT id FROM majors WHERE id = ?',
      [id]
    );

    if (existingMajors.length === 0) {
      return res.status(404).json({
        success: false,
        message: '专业不存在'
      });
    }

    // 检查是否有用户关联到此专业
    const [relatedUsers] = await pool.execute(
      'SELECT id FROM users WHERE major_id = ?',
      [id]
    );

    if (relatedUsers.length > 0) {
      return res.status(400).json({
        success: false,
        message: '无法删除专业，该专业下还有用户'
      });
    }

    // 删除专业
    await pool.execute('DELETE FROM majors WHERE id = ?', [id]);

    res.json({
      success: true,
      message: '专业删除成功'
    });

  } catch (error) {
    console.error('删除专业错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};
