import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

dotenv.config();

const testMySQL = async () => {
  console.log('🔍 测试MySQL连接...');
  console.log('配置信息:');
  console.log(`- 主机: ${process.env.DB_HOST || 'localhost'}`);
  console.log(`- 端口: ${process.env.DB_PORT || 3306}`);
  console.log(`- 用户: ${process.env.DB_USER || 'root'}`);
  console.log(`- 密码: ${process.env.DB_PASSWORD ? '***' : '未设置'}`);
  
  try {
    // 尝试连接到MySQL服务器
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || 'root',
      charset: 'utf8mb4'
    });

    console.log('✅ MySQL连接成功！');
    
    // 检查是否存在目标数据库
    const [databases] = await connection.execute('SHOW DATABASES');
    console.log('📋 现有数据库:');
    databases.forEach(db => {
      console.log(`   - ${db.Database}`);
    });
    
    const dbExists = databases.some(db => db.Database === (process.env.DB_NAME || 'dorm_management'));
    if (dbExists) {
      console.log(`✅ 数据库 '${process.env.DB_NAME || 'dorm_management'}' 已存在`);
    } else {
      console.log(`⚠️  数据库 '${process.env.DB_NAME || 'dorm_management'}' 不存在，需要创建`);
    }
    
    await connection.end();
    console.log('🎉 MySQL测试完成！');
    
  } catch (error) {
    console.error('❌ MySQL连接失败:', error.message);
    console.error('错误代码:', error.code);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 可能的解决方案:');
      console.log('1. 检查MySQL服务是否已启动');
      console.log('2. 如果使用XAMPP，启动XAMPP控制面板并启动MySQL');
      console.log('3. 如果使用MySQL Workbench，确保MySQL服务正在运行');
      console.log('4. 检查防火墙设置');
    } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.log('\n💡 访问被拒绝，请检查:');
      console.log('1. 用户名是否正确');
      console.log('2. 密码是否正确');
      console.log('3. 用户是否有足够的权限');
    }
    
    process.exit(1);
  }
};

testMySQL();
