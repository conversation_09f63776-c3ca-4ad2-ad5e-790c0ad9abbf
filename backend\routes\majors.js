import express from 'express';
import { getMajors, createMajor, updateMajor, deleteMajor } from '../controllers/majorController.js';
import { authenticateToken, requireRole } from '../middleware/auth.js';

const router = express.Router();

// 所有专业路由都需要认证
router.use(authenticateToken);

// GET /api/majors - 获取专业列表
router.get('/', getMajors);

// POST /api/majors - 创建专业（仅系统管理员）
router.post('/', requireRole(['系统管理员']), createMajor);

// PUT /api/majors/:id - 更新专业（仅系统管理员）
router.put('/:id', requireRole(['系统管理员']), updateMajor);

// DELETE /api/majors/:id - 删除专业（仅系统管理员）
router.delete('/:id', requireRole(['系统管理员']), deleteMajor);

export default router;
