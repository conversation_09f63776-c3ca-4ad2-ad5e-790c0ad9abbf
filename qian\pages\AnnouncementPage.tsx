
import React, { useState, useEffect, FormEvent } from 'react';
import { Announcement, UserRole, DormBuilding, College } from '../types';
import { MOCK_DORM_BUILDINGS, MOCK_COLLEGES } from '../constants';
import { useAuth } from '../contexts/AuthContext';
import Button from '../components/Button';
import Modal from '../components/Modal';
import Input from '../components/Input';
import Card from '../components/Card';

const API_BASE_URL = 'http://localhost:3002/api';

const AnnouncementPage: React.FC = () => {
  const { currentUser } = useAuth();
  const [announcements, setAnnouncements] = useState<Announcement[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [newAnnouncement, setNewAnnouncement] = useState<Partial<Announcement>>({ scope: 'All' });
  const [viewingAnnouncement, setViewingAnnouncement] = useState<Announcement | null>(null);

  // 获取公告列表
  const fetchAnnouncements = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_BASE_URL}/announcements`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('获取公告列表失败');
      }

      const data = await response.json();
      if (data.success) {
        // 转换数据格式以匹配前端类型
        const formattedAnnouncements = data.data.map((ann: any) => ({
          id: ann.id,
          title: ann.title,
          content: ann.content,
          authorId: ann.author_id,
          authorName: ann.author_name,
          createdAt: ann.created_at,
          scope: ann.scope,
          targetId: ann.target_id
        }));
        setAnnouncements(formattedAnnouncements);
      }
    } catch (error) {
      console.error('获取公告列表错误:', error);
      alert('获取公告列表失败，请重试');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchAnnouncements();
  }, []);

  if (!currentUser) return null;

  const canCreate = currentUser.role === UserRole.SYSTEM_ADMIN || currentUser.role === UserRole.DORM_ADMIN;

  if (isLoading) {
    return (
      <Card title="通知公告">
        <div className="flex justify-center items-center py-8">
          <div className="text-gray-500">加载中...</div>
        </div>
      </Card>
    );
  }

  const handleOpenModal = () => {
    setNewAnnouncement({ 
        authorId: currentUser.id, 
        authorName: currentUser.name, 
        createdAt: new Date().toISOString(),
        scope: 'All' 
    });
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setNewAnnouncement({ scope: 'All' });
  };
  
  const handleViewAnnouncement = (ann: Announcement) => {
    setViewingAnnouncement(ann);
  };
  
  const handleCloseViewModal = () => {
    setViewingAnnouncement(null);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setNewAnnouncement(prev => ({ ...prev, [name]: value }));
    if (name === "scope" && value === "All") {
        setNewAnnouncement(prev => ({ ...prev, targetId: undefined }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (newAnnouncement.title && newAnnouncement.content) {
      try {
        const token = localStorage.getItem('token');
        const response = await fetch(`${API_BASE_URL}/announcements`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            title: newAnnouncement.title,
            content: newAnnouncement.content,
            scope: newAnnouncement.scope,
            target_id: newAnnouncement.targetId
          }),
        });

        if (!response.ok) {
          throw new Error('发布公告失败');
        }

        const data = await response.json();
        if (data.success) {
          await fetchAnnouncements();
          handleCloseModal();
        }
      } catch (error) {
        console.error('发布公告错误:', error);
        alert('发布公告失败，请重试');
      }
    }
  };
  
  const handleDelete = async (id: string) => {
    if (window.confirm("您确定要删除此公告吗？")) {
      try {
        const token = localStorage.getItem('token');
        const response = await fetch(`${API_BASE_URL}/announcements/${id}`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });

        if (!response.ok) {
          throw new Error('删除公告失败');
        }

        const data = await response.json();
        if (data.success) {
          await fetchAnnouncements();
        }
      } catch (error) {
        console.error('删除公告错误:', error);
        alert('删除公告失败，请重试');
      }
    }
  };

  return (
    <Card 
      title="通知公告"
      actions={canCreate && <Button onClick={handleOpenModal} leftIcon={<i className="fas fa-plus mr-2"></i>}>发布公告</Button>}
    >
      {announcements.length === 0 ? (
        <p className="text-neutral-dark text-center py-4">暂无公告。</p>
      ) : (
        <div className="space-y-4">
          {announcements.sort((a,b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()).map(ann => (
            <div key={ann.id} className="p-4 bg-white rounded-lg shadow border border-neutral-DEFAULT hover:shadow-md transition-shadow">
              <div className="flex justify-between items-start">
                <div>
                    <h3 className="text-xl font-semibold text-primary">{ann.title}</h3>
                    <p className="text-xs text-gray-500 mb-1">
                        由 {ann.authorName} 发布于 {new Date(ann.createdAt).toLocaleDateString()} |范围: {ann.scope === 'All' ? '全体' : ann.scope === 'DormBuilding' ? '指定宿舍楼' : '指定学院'}
                        {ann.scope !== 'All' && ann.targetId && ` (${MOCK_DORM_BUILDINGS.find(b=>b.id === ann.targetId)?.name || MOCK_COLLEGES.find(c=>c.id === ann.targetId)?.name || ann.targetId})`}
                    </p>
                </div>
                <div className="flex space-x-2">
                    <Button size="sm" variant="ghost" onClick={() => handleViewAnnouncement(ann)}>查看</Button>
                    { (currentUser.id === ann.authorId || currentUser.role === UserRole.SYSTEM_ADMIN) && 
                        <Button size="sm" variant="danger" onClick={() => handleDelete(ann.id)}><i className="fas fa-trash"></i></Button>
                    }
                </div>
              </div>
              <p className="text-gray-700 mt-1 truncate">{ann.content}</p>
            </div>
          ))}
        </div>
      )}

      {/* Create New Announcement Modal */}
      <Modal isOpen={isModalOpen} onClose={handleCloseModal} title="发布新公告" size="lg">
        <form onSubmit={handleSubmit} className="space-y-4">
          <Input name="title" label="标题" value={newAnnouncement.title || ''} onChange={handleInputChange} required />
          <div>
            <label htmlFor="content" className="block text-sm font-medium text-gray-700 mb-1">内容</label>
            <textarea
              id="content"
              name="content"
              rows={5}
              value={newAnnouncement.content || ''}
              onChange={handleInputChange}
              className="block w-full px-3 py-2 border border-neutral-DEFAULT rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
              required
            />
          </div>
          <div>
            <label htmlFor="scope" className="block text-sm font-medium text-gray-700 mb-1">发布范围</label>
            <select
              id="scope"
              name="scope"
              value={newAnnouncement.scope}
              onChange={handleInputChange}
              className="block w-full px-3 py-2 border border-neutral-DEFAULT rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
            >
              <option value="All">全体</option>
              <option value="DormBuilding">指定宿舍楼</option>
              { currentUser.role === UserRole.SYSTEM_ADMIN && <option value="College">指定学院</option> }
            </select>
          </div>
          {newAnnouncement.scope === 'DormBuilding' && (
            <div>
              <label htmlFor="targetId" className="block text-sm font-medium text-gray-700 mb-1">选择宿舍楼</label>
              <select
                id="targetId"
                name="targetId"
                value={newAnnouncement.targetId || ''}
                onChange={handleInputChange}
                className="block w-full px-3 py-2 border border-neutral-DEFAULT rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
              >
                <option value="">-- 选择宿舍楼 --</option>
                {MOCK_DORM_BUILDINGS.map(b => <option key={b.id} value={b.id}>{b.name}</option>)}
              </select>
            </div>
          )}
           {newAnnouncement.scope === 'College' && currentUser.role === UserRole.SYSTEM_ADMIN && (
            <div>
              <label htmlFor="targetId" className="block text-sm font-medium text-gray-700 mb-1">选择学院</label>
              <select
                id="targetId"
                name="targetId"
                value={newAnnouncement.targetId || ''}
                onChange={handleInputChange}
                className="block w-full px-3 py-2 border border-neutral-DEFAULT rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
              >
                <option value="">-- 选择学院 --</option>
                {MOCK_COLLEGES.map(c => <option key={c.id} value={c.id}>{c.name}</option>)}
              </select>
            </div>
          )}
          <div className="flex justify-end space-x-2 pt-4">
            <Button type="button" variant="ghost" onClick={handleCloseModal}>取消</Button>
            <Button type="submit">发布公告</Button>
          </div>
        </form>
      </Modal>

      {/* View Announcement Modal */}
      {viewingAnnouncement && (
        <Modal isOpen={!!viewingAnnouncement} onClose={handleCloseViewModal} title={viewingAnnouncement.title} size="lg">
            <div className="space-y-2">
                <p className="text-sm text-gray-500">
                    由 {viewingAnnouncement.authorName} 发布于 {new Date(viewingAnnouncement.createdAt).toLocaleString()}
                </p>
                <p className="text-sm text-gray-500">
                    范围: {viewingAnnouncement.scope === 'All' ? '全体' : viewingAnnouncement.scope === 'DormBuilding' ? '指定宿舍楼' : '指定学院'}
                    {viewingAnnouncement.scope !== 'All' && viewingAnnouncement.targetId && ` (${MOCK_DORM_BUILDINGS.find(b=>b.id === viewingAnnouncement.targetId)?.name || MOCK_COLLEGES.find(c=>c.id === viewingAnnouncement.targetId)?.name || viewingAnnouncement.targetId})`}
                </p>
                <div className="prose max-w-none mt-2 text-gray-700 whitespace-pre-wrap">{viewingAnnouncement.content}</div>
            </div>
             <div className="mt-6 flex justify-end">
                <Button onClick={handleCloseViewModal}>关闭</Button>
            </div>
        </Modal>
      )}
    </Card>
  );
};

export default AnnouncementPage;