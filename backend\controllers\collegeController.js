import { pool } from '../config/database.js';

// 获取学院列表
export const getColleges = async (req, res) => {
  try {
    const [rows] = await pool.execute(
      'SELECT id, name, created_at FROM colleges ORDER BY created_at DESC'
    );

    res.json({
      success: true,
      data: {
        colleges: rows
      }
    });

  } catch (error) {
    console.error('获取学院列表错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};

// 创建学院
export const createCollege = async (req, res) => {
  try {
    const { name } = req.body;

    if (!name) {
      return res.status(400).json({
        success: false,
        message: '学院名称不能为空'
      });
    }

    // 检查学院名称是否已存在
    const [existingColleges] = await pool.execute(
      'SELECT id FROM colleges WHERE name = ?',
      [name]
    );

    if (existingColleges.length > 0) {
      return res.status(400).json({
        success: false,
        message: '该学院名称已存在'
      });
    }

    // 生成学院ID
    const collegeId = `college_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // 插入新学院
    await pool.execute(
      'INSERT INTO colleges (id, name) VALUES (?, ?)',
      [collegeId, name]
    );

    res.status(201).json({
      success: true,
      message: '学院创建成功',
      data: {
        collegeId
      }
    });

  } catch (error) {
    console.error('创建学院错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};

// 更新学院
export const updateCollege = async (req, res) => {
  try {
    const { id } = req.params;
    const { name } = req.body;

    if (!name) {
      return res.status(400).json({
        success: false,
        message: '学院名称不能为空'
      });
    }

    // 检查学院是否存在
    const [existingColleges] = await pool.execute(
      'SELECT id FROM colleges WHERE id = ?',
      [id]
    );

    if (existingColleges.length === 0) {
      return res.status(404).json({
        success: false,
        message: '学院不存在'
      });
    }

    // 检查新名称是否与其他学院重复
    const [duplicateColleges] = await pool.execute(
      'SELECT id FROM colleges WHERE name = ? AND id != ?',
      [name, id]
    );

    if (duplicateColleges.length > 0) {
      return res.status(400).json({
        success: false,
        message: '该学院名称已存在'
      });
    }

    // 更新学院信息
    await pool.execute(
      'UPDATE colleges SET name = ? WHERE id = ?',
      [name, id]
    );

    res.json({
      success: true,
      message: '学院更新成功'
    });

  } catch (error) {
    console.error('更新学院错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};

// 删除学院
export const deleteCollege = async (req, res) => {
  try {
    const { id } = req.params;

    // 检查学院是否存在
    const [existingColleges] = await pool.execute(
      'SELECT id FROM colleges WHERE id = ?',
      [id]
    );

    if (existingColleges.length === 0) {
      return res.status(404).json({
        success: false,
        message: '学院不存在'
      });
    }

    // 检查是否有专业关联到此学院
    const [relatedMajors] = await pool.execute(
      'SELECT id FROM majors WHERE college_id = ?',
      [id]
    );

    if (relatedMajors.length > 0) {
      return res.status(400).json({
        success: false,
        message: '无法删除学院，该学院下还有专业'
      });
    }

    // 检查是否有用户关联到此学院
    const [relatedUsers] = await pool.execute(
      'SELECT id FROM users WHERE college_id = ?',
      [id]
    );

    if (relatedUsers.length > 0) {
      return res.status(400).json({
        success: false,
        message: '无法删除学院，该学院下还有用户'
      });
    }

    // 删除学院
    await pool.execute('DELETE FROM colleges WHERE id = ?', [id]);

    res.json({
      success: true,
      message: '学院删除成功'
    });

  } catch (error) {
    console.error('删除学院错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};
