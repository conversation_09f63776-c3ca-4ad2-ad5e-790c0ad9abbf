import jwt from 'jsonwebtoken';
import { pool } from '../config/database.js';

// JWT认证中间件
export const authenticateToken = async (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

  if (!token) {
    return res.status(401).json({
      success: false,
      message: '访问令牌缺失'
    });
  }

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // 从数据库获取用户信息
    const [rows] = await pool.execute(
      `SELECT u.*, c.name as college_name, m.name as major_name, db.name as dorm_building_name, ur.role_name
       FROM users u
       LEFT JOIN colleges c ON u.college_id = c.id
       LEFT JOIN majors m ON u.major_id = m.id
       LEFT JOIN dorm_buildings db ON u.dorm_building_id = db.id
       LEFT JOIN user_roles ur ON u.role_id = ur.id
       WHERE u.id = ?`,
      [decoded.userId]
    );

    if (rows.length === 0) {
      return res.status(401).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 将用户信息添加到请求对象
    req.user = {
      id: rows[0].id,
      name: rows[0].name,
      email: rows[0].email,
      role: rows[0].role_name,
      phone: rows[0].phone,
      college: rows[0].college_name,
      major: rows[0].major_name,
      dormBuilding: rows[0].dorm_building_name,
      roomNumber: rows[0].room_number,
      emergencyContactName: rows[0].emergency_contact_name,
      emergencyContactPhone: rows[0].emergency_contact_phone
    };

    next();
  } catch (error) {
    console.error('JWT验证错误:', error);
    return res.status(403).json({
      success: false,
      message: '无效的访问令牌'
    });
  }
};

// 角色权限检查中间件
export const requireRole = (allowedRoles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        message: '用户未认证'
      });
    }

    if (!allowedRoles.includes(req.user.role)) {
      return res.status(403).json({
        success: false,
        message: '权限不足'
      });
    }

    next();
  };
};
