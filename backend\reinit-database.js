import { pool } from './config/database.js';
import bcrypt from 'bcrypt';

async function reinitDatabase() {
  try {
    console.log('🔄 重新初始化数据库...');
    
    // 删除现有用户数据（保留管理员）
    console.log('🗑️ 清理现有学生数据...');
    await pool.execute('DELETE FROM users WHERE role_id = 3');
    
    // 生成密码哈希
    const passwordHash = await bcrypt.hash('password123', 10);
    
    // 添加新的学生数据
    console.log('👥 添加新学生数据...');
    const students = [
      { id: 'student01', name: '王五', email: '<EMAIL>', building: 'bldgA' },
      { id: 'student02', name: '赵六', email: '<EMAIL>', building: 'bldgA' },
      { id: 'student03', name: '孙七', email: '<EMAIL>', building: 'bldgA' },
      { id: 'student04', name: '周八', email: 'z<PERSON><PERSON>@example.com', building: 'bldgA' },
      { id: 'student05', name: '吴九', email: '<EMAIL>', building: 'bldgA' },
      { id: 'student06', name: '郑十', email: '<EMAIL>', building: 'bldgB' }
    ];
    
    for (const student of students) {
      await pool.execute(`
        INSERT INTO users (id, name, email, password, role_id, phone, college_id, major_id, dorm_building_id, emergency_contact_name, emergency_contact_phone) 
        VALUES (?, ?, ?, ?, 3, ?, 'college01', 'major01', ?, ?, ?)
      `, [
        student.id, 
        student.name, 
        student.email, 
        passwordHash,
        '138' + String(Math.floor(Math.random() * 100000000)).padStart(8, '0'),
        student.building,
        student.name.slice(0, 1) + '父',
        '139' + String(Math.floor(Math.random() * 100000000)).padStart(8, '0')
      ]);
      console.log(`✅ 添加学生: ${student.name} -> ${student.building}`);
    }
    
    // 检查结果
    console.log('\n📊 检查结果:');
    const [allUsers] = await pool.execute('SELECT COUNT(*) as count FROM users');
    const [students_count] = await pool.execute('SELECT COUNT(*) as count FROM users WHERE role_id = 3');
    const [buildingA_students] = await pool.execute('SELECT COUNT(*) as count FROM users WHERE role_id = 3 AND dorm_building_id = "bldgA"');
    
    console.log(`总用户数: ${allUsers[0].count}`);
    console.log(`学生总数: ${students_count[0].count}`);
    console.log(`A栋学生数: ${buildingA_students[0].count}`);
    
    // 显示A栋学生列表
    const [buildingA_list] = await pool.execute(`
      SELECT id, name, dorm_building_id, room_number 
      FROM users 
      WHERE role_id = 3 AND dorm_building_id = 'bldgA' 
      ORDER BY name
    `);
    
    console.log('\nA栋学生列表:');
    buildingA_list.forEach(s => {
      console.log(`  - ${s.name} (${s.id}) - 房间: ${s.room_number || '未分配'}`);
    });
    
    await pool.end();
    console.log('\n🎉 数据库重新初始化完成！');
    console.log('张三现在应该能在学生分配页面看到5个A栋学生了！');
  } catch (error) {
    console.error('❌ 重新初始化失败:', error);
    process.exit(1);
  }
}

reinitDatabase();
