import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

dotenv.config();

const testDatabaseStatus = async () => {
  let connection;
  
  try {
    console.log('🔍 检查数据库状态...');
    
    // 连接到MySQL服务器
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || 'root',
      charset: 'utf8mb4'
    });

    console.log('✅ 成功连接到MySQL服务器');

    // 检查数据库是否存在
    const [databases] = await connection.execute('SHOW DATABASES');
    const dbExists = databases.some(db => db.Database === 'dorm_management');
    
    console.log('\n📋 现有数据库:');
    databases.forEach(db => {
      const marker = db.Database === 'dorm_management' ? ' ✅' : '';
      console.log(`   - ${db.Database}${marker}`);
    });

    if (!dbExists) {
      console.log('\n❌ dorm_management 数据库不存在');
      return;
    }

    // 使用数据库
    await connection.execute('USE dorm_management');
    console.log('\n✅ 成功切换到 dorm_management 数据库');

    // 检查表是否存在
    const [tables] = await connection.execute('SHOW TABLES');
    console.log('\n📋 现有表:');
    
    if (tables.length === 0) {
      console.log('   ❌ 没有表');
    } else {
      tables.forEach(table => {
        console.log(`   - ${Object.values(table)[0]}`);
      });
    }

    // 检查用户表是否存在并有数据
    const userTableExists = tables.some(table => Object.values(table)[0] === 'users');
    
    if (userTableExists) {
      const [userCount] = await connection.execute('SELECT COUNT(*) as count FROM users');
      console.log(`\n👥 用户表记录数: ${userCount[0].count}`);
      
      if (userCount[0].count > 0) {
        const [users] = await connection.execute('SELECT id, name, email FROM users LIMIT 3');
        console.log('\n📋 示例用户:');
        users.forEach(user => {
          console.log(`   - ${user.name} (${user.email})`);
        });
      }
    } else {
      console.log('\n❌ users 表不存在');
    }

  } catch (error) {
    console.error('❌ 检查失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
};

testDatabaseStatus();
