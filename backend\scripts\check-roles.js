import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

dotenv.config();

const checkRoles = async () => {
  let connection;
  
  try {
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || 'root',
      database: process.env.DB_NAME || 'dorm_management',
      charset: 'utf8mb4'
    });

    console.log('✅ 连接到数据库成功');

    // 检查角色表结构
    console.log('📋 用户角色表结构:');
    const [roleColumns] = await connection.execute('DESCRIBE user_roles');
    roleColumns.forEach(col => {
      console.log(`   - ${col.Field}: ${col.Type} ${col.Null === 'NO' ? 'NOT NULL' : 'NULL'} ${col.Key ? `(${col.Key})` : ''}`);
    });

    // 检查角色数据
    console.log('\n📋 现有角色:');
    const [roles] = await connection.execute('SELECT * FROM user_roles');
    roles.forEach(role => {
      console.log(`   - ID: ${role.id}, 名称: ${role.name}`);
    });

    // 检查现有用户
    console.log('\n📋 现有用户:');
    const [users] = await connection.execute(`
      SELECT u.id, u.name, u.email, ur.name as role_name 
      FROM users u 
      LEFT JOIN user_roles ur ON u.role_id = ur.id 
      LIMIT 10
    `);
    users.forEach(user => {
      console.log(`   - ${user.name} (${user.email}) - 角色: ${user.role_name || '未分配'}`);
    });
    
  } catch (error) {
    console.error('❌ 检查失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
};

checkRoles();
