# 易宿管 - 智能宿舍管理系统

一个基于 React + TypeScript + Node.js + MySQL 的现代化宿舍管理系统，提供完整的宿舍管理解决方案。

## 🚀 项目特色

- **多角色管理**: 系统管理员、宿舍管理员、学生、维修人员
- **完整功能**: 用户管理、宿舍分配、维修请求、公告管理、水电费管理等
- **现代化UI**: 响应式设计，美观的用户界面
- **实时数据**: 前后端分离，实时数据同步
- **安全认证**: JWT认证，角色权限控制

## 📋 系统功能

### 系统管理员
- 用户管理（增删改查）
- 学院、专业管理
- 宿舍楼管理
- 系统公告发布

### 宿舍管理员
- 房间管理
- 学生分配
- 违规记录
- 晚归记录
- 访客管理
- 文明宿舍评分

### 学生
- 个人信息管理
- 维修请求提交
- 水电费查询
- 公告查看

### 维修人员
- 维修任务处理
- 维修进度更新
- 维修记录查看

## 🛠️ 技术栈

### 前端
- React 19.1.0
- TypeScript
- React Router DOM
- Vite
- Tailwind CSS

### 后端
- Node.js
- Express.js
- MySQL 8.0
- JWT认证
- CORS支持

## 📦 项目结构

```
易宿管/
├── frontend/                 # 前端项目
│   ├── src/
│   │   ├── components/      # 通用组件
│   │   ├── pages/          # 页面组件
│   │   ├── contexts/       # React上下文
│   │   ├── layouts/        # 布局组件
│   │   ├── types.ts        # TypeScript类型定义
│   │   └── constants.ts    # 常量定义
│   ├── package.json
│   └── vite.config.ts
├── backend/                 # 后端项目
│   ├── config/             # 配置文件
│   ├── controllers/        # 控制器
│   ├── middleware/         # 中间件
│   ├── routes/            # 路由
│   ├── scripts/           # 脚本文件
│   └── server.js          # 主服务器文件
├── database/               # 数据库文件
│   └── cursor.sql         # 数据库初始化脚本
└── README.md
```

## 🚀 快速开始

### 前置要求

- Node.js 18+ 
- MySQL 8.0+
- npm 或 yarn

### 1. 克隆项目

```bash
git clone <项目地址>
cd 易宿管
```

### 2. 数据库设置

#### 2.1 启动MySQL服务

确保MySQL服务正在运行，并且用户名和密码都是 `root`

#### 2.2 初始化数据库

```bash
# 进入后端目录
cd backend

# 安装依赖
npm install

# 初始化数据库
node scripts/init-database.js
```

这将创建名为 `cursor` 的数据库，并导入所有表结构和初始数据。

### 3. 启动后端服务

```bash
# 在backend目录下
npm install
npm run dev
```

后端服务将在 `http://localhost:3001` 启动

### 4. 启动前端服务

```bash
# 在项目根目录下
npm install
npm run dev
```

前端服务将在 `http://localhost:5174` 启动

## 🔐 默认账户

系统预置了以下测试账户：

### 系统管理员
- 邮箱: `<EMAIL>`
- 密码: `password123`
- 角色: 系统管理员

### 宿舍管理员
- 邮箱: `<EMAIL>`
- 密码: `password123`
- 角色: 宿舍管理员 (A栋)

- 邮箱: `<EMAIL>`
- 密码: `password123`
- 角色: 宿舍管理员 (B栋)

### 学生
- 邮箱: `<EMAIL>`
- 密码: `password123`
- 角色: 学生 (A栋101)

- 邮箱: `<EMAIL>`
- 密码: `password123`
- 角色: 学生 (B栋205)

- 邮箱: `<EMAIL>`
- 密码: `password123`
- 角色: 学生 (A栋102)

### 维修人员
- 邮箱: `<EMAIL>`
- 密码: `password123`
- 角色: 维修人员

## 📚 API文档

详细的API文档请查看: `backend/API_DOCUMENTATION.md`

### 主要API端点

- `POST /api/auth/login` - 用户登录
- `POST /api/auth/register` - 用户注册
- `GET /api/users` - 获取用户列表 (管理员)
- `GET /api/repairs` - 获取维修请求
- `POST /api/repairs` - 创建维修请求 (学生)

## 🔧 开发指南

### 添加新的API端点

1. 在 `backend/controllers/` 创建控制器
2. 在 `backend/routes/` 创建路由
3. 在 `backend/server.js` 注册路由

### 添加新的前端页面

1. 在 `src/pages/` 创建页面组件
2. 在 `src/types.ts` 添加类型定义
3. 在 `App.tsx` 添加路由

## 🐛 故障排除

### 数据库连接失败
- 确保MySQL服务正在运行
- 检查用户名和密码是否正确
- 确保数据库 `cursor` 已创建

### 前端无法连接后端
- 确保后端服务在 `http://localhost:3001` 运行
- 检查CORS配置
- 查看浏览器控制台错误信息

### 登录失败
- 检查邮箱和密码是否正确
- 确保选择了正确的角色
- 查看后端日志获取详细错误信息

## 📝 更新日志

### v1.0.0
- 初始版本发布
- 完整的用户管理系统
- 维修请求功能
- 多角色权限控制

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

MIT License

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- 邮箱: [<EMAIL>]
- 项目地址: [项目GitHub地址]

---

**易宿管团队** - 让宿舍管理更智能、更高效！
