-- 插入测试数据
USE dorm_management;

-- 1. 插入学院数据
INSERT INTO colleges (id, name) VALUES
('college01', '计算机科学学院'),
('college02', '电子工程学院'),
('college03', '机械工程学院'),
('college04', '经济管理学院');

-- 2. 插入专业数据
INSERT INTO majors (id, name, college_id) VALUES
('major01', '计算机科学与技术', 'college01'),
('major02', '软件工程', 'college01'),
('major03', '电子信息工程', 'college02'),
('major04', '通信工程', 'college02'),
('major05', '机械设计制造及其自动化', 'college03'),
('major06', '工商管理', 'college04'),
('major07', '会计学', 'college04');

-- 3. 插入宿舍楼数据
INSERT INTO dorm_buildings (id, name, floors, total_rooms) VALUES
('bldgA', 'A栋', 6, 120),
('bldgB', 'B栋', 8, 160),
('bldgC', 'C栋', 5, 100);

-- 4. 插入用户数据（先插入不依赖其他用户的用户）
INSERT INTO users (id, name, email, password, role, phone) VALUES
-- 系统管理员
('sysadmin01', '系统管理员', '<EMAIL>', '$2b$10$rOvHPGkwYvYFQVlXiXHxHOuBJKxj8qMxqGjZvQjZvQjZvQjZvQjZvO', '系统管理员', '13800000001'),

-- 维修人员
('repair01', '爱德华·修理工', '<EMAIL>', '$2b$10$rOvHPGkwYvYFQVlXiXHxHOuBJKxj8qMxqGjZvQjZvQjZvQjZvQjZvO', '维修人员', '13800000005');

-- 插入宿舍管理员
INSERT INTO users (id, name, email, password, role, phone, dorm_building_id) VALUES
('dormadmin01', '张三', '<EMAIL>', '$2b$10$rOvHPGkwYvYFQVlXiXHxHOuBJKxj8qMxqGjZvQjZvQjZvQjZvQjZvO', '宿舍管理员', '13800000002', 'bldgA'),
('dormadmin02', '李四', '<EMAIL>', '$2b$10$rOvHPGkwYvYFQVlXiXHxHOuBJKxj8qMxqGjZvQjZvQjZvQjZvQjZvO', '宿舍管理员', '13800000003', 'bldgB');

-- 更新宿舍楼的管理员分配
UPDATE dorm_buildings SET assigned_admin_id = 'dormadmin01' WHERE id = 'bldgA';
UPDATE dorm_buildings SET assigned_admin_id = 'dormadmin02' WHERE id = 'bldgB';

-- 5. 插入房间数据
INSERT INTO rooms (id, room_number, dorm_building_id, floor, type, capacity, occupied_beds) VALUES
-- A栋房间
('roomA101', '101', 'bldgA', 1, '四人间', 4, 2),
('roomA102', '102', 'bldgA', 1, '四人间', 4, 1),
('roomA201', '201', 'bldgA', 2, '四人间', 4, 0),
('roomA202', '202', 'bldgA', 2, '四人间', 4, 0),

-- B栋房间
('roomB205', '205', 'bldgB', 2, '四人间', 4, 1),
('roomB301', '301', 'bldgB', 3, '四人间', 4, 0),
('roomB302', '302', 'bldgB', 3, '四人间', 4, 0);

-- 6. 插入学生用户
INSERT INTO users (id, name, email, password, role, phone, college_id, major_id, dorm_building_id, room_number, emergency_contact_name, emergency_contact_phone) VALUES
('student01', '王五', '<EMAIL>', '$2b$10$rOvHPGkwYvYFQVlXiXHxHOuBJKxj8qMxqGjZvQjZvQjZvQjZvQjZvO', '学生', '13800000004', 'college01', 'major01', 'bldgA', '101', '王父', '13900000001'),
('student02', '赵六', '<EMAIL>', '$2b$10$rOvHPGkwYvYFQVlXiXHxHOuBJKxj8qMxqGjZvQjZvQjZvQjZvQjZvO', '学生', '13800000006', 'college02', 'major03', 'bldgB', '205', '赵母', '13900000002'),
('student03', '孙七', '<EMAIL>', '$2b$10$rOvHPGkwYvYFQVlXiXHxHOuBJKxj8qMxqGjZvQjZvQjZvQjZvQjZvO', '学生', '13800000007', 'college01', 'major02', 'bldgA', '102', '孙父', '13900000003');

-- 7. 插入床位数据
INSERT INTO beds (id, room_id, bed_number, status, student_id) VALUES
-- A101房间床位
('bedA101_1', 'roomA101', '1', '已入住', 'student01'),
('bedA101_2', 'roomA101', '2', '已入住', 'student03'),
('bedA101_3', 'roomA101', '3', '空闲', NULL),
('bedA101_4', 'roomA101', '4', '空闲', NULL),

-- A102房间床位
('bedA102_1', 'roomA102', '1', '已入住', 'student03'),
('bedA102_2', 'roomA102', '2', '空闲', NULL),
('bedA102_3', 'roomA102', '3', '空闲', NULL),
('bedA102_4', 'roomA102', '4', '空闲', NULL),

-- B205房间床位
('bedB205_1', 'roomB205', '1', '已入住', 'student02'),
('bedB205_2', 'roomB205', '2', '空闲', NULL),
('bedB205_3', 'roomB205', '3', '空闲', NULL),
('bedB205_4', 'roomB205', '4', '空闲', NULL);

-- 8. 插入公告数据
INSERT INTO announcements (id, title, content, author_id, scope, target_id) VALUES
('anno001', '近期维修安排通知', '请注意，A栋将于7月25日上午9点至下午5点进行计划内维修。', 'sysadmin01', 'All', NULL),
('anno002', '宿舍会议 - B栋', 'B栋全体住宿学生务必参加7月26日晚上7点在公共活动室举行的会议。', 'dormadmin02', 'DormBuilding', 'bldgB');

-- 9. 插入维修请求数据
INSERT INTO repair_requests (id, student_id, room_id, room_number, dorm_building_id, description, status, assigned_staff_id, notes) VALUES
('repair001', 'student01', 'roomA101', '101', 'bldgA', '水龙头漏水', '待处理', NULL, NULL),
('repair002', 'student02', 'roomB205', '205', 'bldgB', '灯泡坏了', '已指派', 'repair01', '已安排维修人员'),
('repair003', 'student03', 'roomA102', '102', 'bldgA', '门锁损坏', '已完成', 'repair01', '已更换新门锁');

-- 10. 插入水电费账单数据
INSERT INTO utility_bills (id, student_id, room_id, month, electricity_usage, electricity_cost, water_usage, water_cost, total_cost, is_paid) VALUES
('bill001', 'student01', 'roomA101', '2024-06', 120.50, 60.25, 15.30, 30.60, 90.85, TRUE),
('bill002', 'student01', 'roomA101', '2024-07', 135.20, 67.60, 18.40, 36.80, 104.40, FALSE),
('bill003', 'student02', 'roomB205', '2024-06', 98.30, 49.15, 12.20, 24.40, 73.55, TRUE),
('bill004', 'student02', 'roomB205', '2024-07', 110.80, 55.40, 14.50, 29.00, 84.40, FALSE);

-- 11. 插入违规记录数据
INSERT INTO violations (id, student_id, dorm_building_id, date, type, description, action_taken, recorded_by) VALUES
('viol001', 'student01', 'bldgA', '2024-07-15', '噪音投诉', '深夜大声播放音乐', '口头警告', 'dormadmin01'),
('viol002', 'student02', 'bldgB', '2024-07-18', '违禁物品', '在宿舍内使用大功率电器', '没收物品并书面警告', 'dormadmin02');

-- 12. 插入晚归记录数据
INSERT INTO late_returns (id, student_id, dorm_building_id, date, time, reason, recorded_by) VALUES
('late001', 'student01', 'bldgA', '2024-07-20', '23:30:00', '参加社团活动', 'dormadmin01'),
('late002', 'student03', 'bldgA', '2024-07-22', '23:45:00', '图书馆学习', 'dormadmin01');

-- 13. 插入访客记录数据
INSERT INTO visitors (id, visitor_name, visitor_id_number, reason, entry_time, exit_time, visited_student_id, dorm_building_id, recorded_by) VALUES
('vis001', '访客甲', '123456789012345678', '探亲', '2024-07-20 14:00:00', '2024-07-20 16:00:00', 'student01', 'bldgA', 'dormadmin01'),
('vis002', '访客乙', '987654321098765432', '送东西', '2024-07-21 10:00:00', NULL, 'student02', 'bldgB', 'dormadmin02');

-- 14. 插入文明宿舍评分数据
INSERT INTO civilized_dorm_scores (id, room_id, dorm_building_id, date, score, notes, recorded_by) VALUES
('cds001', 'roomA101', 'bldgA', '2024-07-01', 95, '卫生良好，物品摆放整齐。', 'dormadmin01'),
('cds002', 'roomB205', 'bldgB', '2024-07-01', 88, '阳台有杂物，已提醒。', 'dormadmin02'),
('cds003', 'roomA102', 'bldgA', '2024-07-01', 92, '整体不错。', 'dormadmin01');
