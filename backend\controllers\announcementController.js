import { pool } from '../config/database.js';

// 获取所有公告
export const getAnnouncements = async (req, res) => {
  try {
    const [announcements] = await pool.execute(`
      SELECT a.*, u.name as author_name
      FROM announcements a
      LEFT JOIN users u ON a.author_id = u.id
      ORDER BY a.created_at DESC
    `);

    res.json({
      success: true,
      data: announcements
    });

  } catch (error) {
    console.error('获取公告列表错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};

// 创建公告
export const createAnnouncement = async (req, res) => {
  try {
    const { 
      title, 
      content, 
      scope = 'All', 
      target_id 
    } = req.body;

    if (!title || !content) {
      return res.status(400).json({
        success: false,
        message: '标题和内容都是必填项'
      });
    }

    // 从JWT token中获取用户信息
    const author_id = req.user.id;

    // 生成公告ID
    const announcementId = `anno_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // 插入新公告
    await pool.execute(
      `INSERT INTO announcements (
        id, title, content, author_id, scope, target_id
      ) VALUES (?, ?, ?, ?, ?, ?)`,
      [
        announcementId, title, content, author_id, scope, target_id || null
      ]
    );

    res.status(201).json({
      success: true,
      message: '公告发布成功',
      data: {
        announcementId
      }
    });

  } catch (error) {
    console.error('创建公告错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};

// 更新公告
export const updateAnnouncement = async (req, res) => {
  try {
    const { id } = req.params;
    const { 
      title, 
      content, 
      scope, 
      target_id 
    } = req.body;

    // 检查公告是否存在
    const [existingAnnouncements] = await pool.execute('SELECT author_id FROM announcements WHERE id = ?', [id]);
    if (existingAnnouncements.length === 0) {
      return res.status(404).json({
        success: false,
        message: '公告不存在'
      });
    }

    // 检查权限（只有作者或系统管理员可以编辑）
    const announcement = existingAnnouncements[0];
    if (announcement.author_id !== req.user.id && req.user.role_id !== 1) {
      return res.status(403).json({
        success: false,
        message: '没有权限编辑此公告'
      });
    }

    // 更新公告信息
    await pool.execute(
      `UPDATE announcements SET 
        title = ?, content = ?, scope = ?, target_id = ?
       WHERE id = ?`,
      [
        title, content, scope, target_id || null, id
      ]
    );

    res.json({
      success: true,
      message: '公告更新成功'
    });

  } catch (error) {
    console.error('更新公告错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};

// 删除公告
export const deleteAnnouncement = async (req, res) => {
  try {
    const { id } = req.params;

    // 检查公告是否存在
    const [existingAnnouncements] = await pool.execute('SELECT author_id FROM announcements WHERE id = ?', [id]);
    if (existingAnnouncements.length === 0) {
      return res.status(404).json({
        success: false,
        message: '公告不存在'
      });
    }

    // 检查权限（只有作者或系统管理员可以删除）
    const announcement = existingAnnouncements[0];
    if (announcement.author_id !== req.user.id && req.user.role_id !== 1) {
      return res.status(403).json({
        success: false,
        message: '没有权限删除此公告'
      });
    }

    // 删除公告
    await pool.execute('DELETE FROM announcements WHERE id = ?', [id]);

    res.json({
      success: true,
      message: '公告删除成功'
    });

  } catch (error) {
    console.error('删除公告错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};

// 获取单个公告详情
export const getAnnouncementById = async (req, res) => {
  try {
    const { id } = req.params;

    const [announcements] = await pool.execute(`
      SELECT a.*, u.name as author_name
      FROM announcements a
      LEFT JOIN users u ON a.author_id = u.id
      WHERE a.id = ?
    `, [id]);

    if (announcements.length === 0) {
      return res.status(404).json({
        success: false,
        message: '公告不存在'
      });
    }

    res.json({
      success: true,
      data: announcements[0]
    });

  } catch (error) {
    console.error('获取公告详情错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};
