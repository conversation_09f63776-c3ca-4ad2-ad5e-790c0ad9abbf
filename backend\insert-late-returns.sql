-- 创建晚归记录表（如果不存在）
CREATE TABLE IF NOT EXISTS late_returns (
  id VARCHAR(50) PRIMARY KEY,
  student_id VARCHAR(50) NOT NULL,
  dorm_building_id VARCHAR(50) NOT NULL,
  date DATE NOT NULL,
  time TIME NOT NULL,
  reason TEXT,
  recorded_by VARCHAR(50) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (dorm_building_id) REFERENCES dorm_buildings(id) ON DELETE CASCADE,
  FOREIGN KEY (recorded_by) REFERENCES users(id) ON DELETE CASCADE
);

-- 清空现有数据
DELETE FROM late_returns;

-- 插入A栋晚归记录测试数据
INSERT INTO late_returns (id, student_id, dorm_building_id, date, time, reason, recorded_by) VALUES
('late001', 'student01', 'bldgA', '2024-07-18', '23:30:00', '图书馆学习到很晚', 'dormadmin01'),
('late002', 'student03', 'bldgA', '2024-07-19', '00:15:00', '社团活动结束较晚', 'dormadmin01'),
('late003', 'student04', 'bldgA', '2024-07-20', '23:45:00', '实验室项目加班', 'dormadmin01'),
('late004', 'student01', 'bldgA', '2024-07-21', '00:30:00', '朋友聚会', 'dormadmin01'),
('late005', 'student05', 'bldgA', '2024-07-22', '23:50:00', '兼职工作结束晚', 'dormadmin01');

-- 查看插入结果
SELECT 
  lr.id,
  u.name as student_name,
  lr.date,
  lr.time,
  lr.reason,
  lr.dorm_building_id
FROM late_returns lr
LEFT JOIN users u ON lr.student_id = u.id
WHERE lr.dorm_building_id = 'bldgA'
ORDER BY lr.date DESC, lr.time DESC;
