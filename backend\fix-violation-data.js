import { pool } from './config/database.js';

async function fixViolationData() {
  try {
    console.log('🔧 修复违规记录数据...');
    
    // 更新现有的错误测试数据
    await pool.execute(`
      UPDATE violations 
      SET student_id = 'student01', recorded_by = 'dormadmin01' 
      WHERE id = 'viol001'
    `);
    
    await pool.execute(`
      UPDATE violations 
      SET student_id = 'student02', recorded_by = 'dormadmin01' 
      WHERE id = 'viol002'
    `);
    
    console.log('✅ 违规记录数据修复完成');
    
    // 验证修复结果
    console.log('\n📊 验证修复结果:');
    const [violations] = await pool.execute(`
      SELECT v.id, v.student_id, u.name as student_name, v.type, v.description
      FROM violations v
      LEFT JOIN users u ON v.student_id = u.id
      ORDER BY v.created_at DESC
    `);
    
    violations.forEach((v, i) => {
      console.log(`违规记录 ${i+1}:`, {
        id: v.id,
        student_id: v.student_id,
        student_name: v.student_name,
        type: v.type
      });
    });
    
    await pool.end();
    console.log('\n🎉 修复完成！');
  } catch (error) {
    console.error('❌ 修复失败:', error);
    process.exit(1);
  }
}

fixViolationData();
