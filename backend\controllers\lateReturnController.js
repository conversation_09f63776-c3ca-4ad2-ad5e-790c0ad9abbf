import { pool } from '../config/database.js';

// 获取晚归记录列表（支持按宿舍楼筛选）
export const getLateReturns = async (req, res) => {
  try {
    const { building_id } = req.query;
    
    console.log('获取晚归记录请求:', { building_id, user: req.user?.id });
    
    let query = `
      SELECT lr.*, u.name as student_name, u.room_number, db.name as building_name
      FROM late_returns lr
      LEFT JOIN users u ON lr.student_id = u.id
      LEFT JOIN dorm_buildings db ON lr.dorm_building_id = db.id
    `;
    let params = [];

    if (building_id) {
      query += ' WHERE lr.dorm_building_id = ?';
      params.push(building_id);
    }

    query += ' ORDER BY lr.date DESC, lr.time DESC';

    console.log('查询SQL:', query);
    console.log('查询参数:', params);

    const [lateReturns] = await pool.execute(query, params);

    console.log(`查询结果: ${lateReturns.length} 条晚归记录`);
    lateReturns.forEach(lr => {
      console.log(`  - ${lr.student_name} (${lr.student_id}) - 日期: ${lr.date} 时间: ${lr.time}`);
    });

    res.json({
      success: true,
      data: lateReturns
    });

  } catch (error) {
    console.error('获取晚归记录错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};

// 获取晚归记录详情
export const getLateReturnDetails = async (req, res) => {
  try {
    const { id } = req.params;
    
    const [lateReturns] = await pool.execute(`
      SELECT lr.*, u.name as student_name, u.room_number, db.name as building_name
      FROM late_returns lr
      LEFT JOIN users u ON lr.student_id = u.id
      LEFT JOIN dorm_buildings db ON lr.dorm_building_id = db.id
      WHERE lr.id = ?
    `, [id]);

    if (lateReturns.length === 0) {
      return res.status(404).json({
        success: false,
        message: '晚归记录不存在'
      });
    }

    res.json({
      success: true,
      data: lateReturns[0]
    });

  } catch (error) {
    console.error('获取晚归记录详情错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};

// 创建晚归记录
export const createLateReturn = async (req, res) => {
  try {
    const { student_id, dorm_building_id, date, time, reason } = req.body;
    const recorded_by = req.user.id;
    
    // 生成唯一ID
    const lateReturnId = `lr_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    console.log('创建晚归记录参数:', {
      lateReturnId,
      student_id,
      dorm_building_id,
      date,
      time,
      reason,
      recorded_by
    });

    // 验证学生是否存在
    const [students] = await pool.execute(
      'SELECT id, name FROM users WHERE id = ? AND role_id = 3',
      [student_id]
    );

    if (students.length === 0) {
      return res.status(400).json({
        success: false,
        message: '学生不存在'
      });
    }

    // 插入晚归记录
    await pool.execute(`
      INSERT INTO late_returns (
        id, student_id, dorm_building_id, date, time, reason, recorded_by
      ) VALUES (?, ?, ?, ?, ?, ?, ?)
    `, [lateReturnId, student_id, dorm_building_id, date, time, reason, recorded_by]);

    console.log('✅ 晚归记录创建成功:', lateReturnId);

    res.status(201).json({
      success: true,
      message: '晚归记录创建成功',
      data: { id: lateReturnId }
    });

  } catch (error) {
    console.error('创建晚归记录错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};

// 更新晚归记录
export const updateLateReturn = async (req, res) => {
  try {
    const { id } = req.params;
    const { student_id, dorm_building_id, date, time, reason } = req.body;
    
    // 检查记录是否存在
    const [existing] = await pool.execute(
      'SELECT id FROM late_returns WHERE id = ?',
      [id]
    );

    if (existing.length === 0) {
      return res.status(404).json({
        success: false,
        message: '晚归记录不存在'
      });
    }

    // 更新记录
    await pool.execute(`
      UPDATE late_returns 
      SET student_id = ?, dorm_building_id = ?, date = ?, time = ?, reason = ?
      WHERE id = ?
    `, [student_id, dorm_building_id, date, time, reason, id]);

    console.log('✅ 晚归记录更新成功:', id);

    res.json({
      success: true,
      message: '晚归记录更新成功'
    });

  } catch (error) {
    console.error('更新晚归记录错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};

// 删除晚归记录
export const deleteLateReturn = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 检查记录是否存在
    const [existing] = await pool.execute(
      'SELECT id FROM late_returns WHERE id = ?',
      [id]
    );

    if (existing.length === 0) {
      return res.status(404).json({
        success: false,
        message: '晚归记录不存在'
      });
    }

    // 删除记录
    await pool.execute('DELETE FROM late_returns WHERE id = ?', [id]);

    console.log('✅ 晚归记录删除成功:', id);

    res.json({
      success: true,
      message: '晚归记录删除成功'
    });

  } catch (error) {
    console.error('删除晚归记录错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};
