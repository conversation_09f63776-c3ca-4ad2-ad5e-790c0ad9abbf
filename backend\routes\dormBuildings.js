import express from 'express';
import { 
  getDormBuildings, 
  createDormBuilding, 
  updateDormBuilding, 
  deleteDormBuilding 
} from '../controllers/dormBuildingController.js';
import { authenticateToken } from '../middleware/auth.js';

const router = express.Router();

// 获取所有宿舍楼
router.get('/', authenticateToken, getDormBuildings);

// 创建宿舍楼
router.post('/', authenticateToken, createDormBuilding);

// 更新宿舍楼
router.put('/:id', authenticateToken, updateDormBuilding);

// 删除宿舍楼
router.delete('/:id', authenticateToken, deleteDormBuilding);

export default router;
