import React from 'react';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'danger' | 'ghost' | 'gradient';
  size?: 'sm' | 'md' | 'lg';
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  isLoading?: boolean;
  rounded?: 'sm' | 'md' | 'lg' | 'full';
}

const Button: React.FC<ButtonProps> = ({
  children,
  variant = 'primary',
  size = 'md',
  leftIcon,
  rightIcon,
  className = '',
  isLoading = false,
  disabled,
  rounded = 'md',
  ...props
}) => {
  const baseStyle = 'font-semibold focus:outline-none focus:ring-2 focus:ring-opacity-50 transition-all duration-300 ease-in-out inline-flex items-center justify-center relative overflow-hidden';
  
  let variantStyle = '';
  switch (variant) {
    case 'primary':
      variantStyle = 'bg-gradient-to-r from-blue-600 to-blue-700 text-white hover:from-blue-700 hover:to-blue-800 focus:ring-blue-500 shadow-lg hover:shadow-xl';
      break;
    case 'secondary':
      variantStyle = 'bg-gradient-to-r from-purple-600 to-purple-700 text-white hover:from-purple-700 hover:to-purple-800 focus:ring-purple-500 shadow-lg hover:shadow-xl';
      break;
    case 'danger':
      variantStyle = 'bg-gradient-to-r from-red-500 to-red-600 text-white hover:from-red-600 hover:to-red-700 focus:ring-red-500 shadow-lg hover:shadow-xl';
      break;
    case 'ghost':
      variantStyle = 'bg-transparent text-gray-700 hover:bg-gray-100 focus:ring-gray-500 border border-gray-300 hover:border-gray-400';
      break;
    case 'gradient':
      variantStyle = 'bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 text-white hover:from-blue-600 hover:via-purple-600 hover:to-pink-600 focus:ring-purple-500 shadow-lg hover:shadow-xl';
      break;
  }

  let sizeStyle = '';
  switch (size) {
    case 'sm':
      sizeStyle = 'px-4 py-2 text-sm';
      break;
    case 'md':
      sizeStyle = 'px-6 py-3 text-base';
      break;
    case 'lg':
      sizeStyle = 'px-8 py-4 text-lg';
      break;
  }

  let roundedStyle = '';
  switch (rounded) {
    case 'sm':
      roundedStyle = 'rounded';
      break;
    case 'md':
      roundedStyle = 'rounded-lg';
      break;
    case 'lg':
      roundedStyle = 'rounded-xl';
      break;
    case 'full':
      roundedStyle = 'rounded-full';
      break;
  }

  const disabledStyle = isLoading || disabled ? 'opacity-50 cursor-not-allowed transform-none' : 'hover:scale-105 active:scale-95';

  return (
    <button
      className={`${baseStyle} ${variantStyle} ${sizeStyle} ${roundedStyle} ${disabledStyle} ${className}`}
      disabled={isLoading || disabled}
      {...props}
    >
      {isLoading && <SpinnerIcon />}
      {leftIcon && !isLoading && <span className="mr-2">{leftIcon}</span>}
      {children}
      {rightIcon && !isLoading && <span className="ml-2">{rightIcon}</span>}
      
      {/* 悬停光效 */}
      {!isLoading && !disabled && (
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-0 hover:opacity-20 transition-opacity duration-500 transform -skew-x-12 -translate-x-full hover:translate-x-full"></div>
      )}
    </button>
  );
};

const SpinnerIcon: React.FC = () => (
  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
  </svg>
);

export default Button;
    