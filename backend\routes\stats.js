import express from 'express';
import { 
  getSystemStats, 
  getLatestAnnouncements, 
  getLatestRepairs 
} from '../controllers/statsController.js';
import { authenticateToken } from '../middleware/auth.js';

const router = express.Router();

// 获取系统统计数据
router.get('/system', authenticateToken, getSystemStats);

// 获取最新公告
router.get('/announcements/latest', authenticateToken, getLatestAnnouncements);

// 获取最新维修请求
router.get('/repairs/latest', authenticateToken, getLatestRepairs);

export default router;
