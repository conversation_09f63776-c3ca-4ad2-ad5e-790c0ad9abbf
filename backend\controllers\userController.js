import bcrypt from 'bcrypt';
import { pool } from '../config/database.js';

// 获取用户列表
export const getUsers = async (req, res) => {
  try {
    const [rows] = await pool.execute(
      `SELECT u.id, u.name, u.email, ur.role_name as role, u.phone, u.room_number,
              u.emergency_contact_name, u.emergency_contact_phone,
              c.name as college, m.name as major, db.name as dormBuilding
       FROM users u
       LEFT JOIN colleges c ON u.college_id = c.id
       LEFT JOIN majors m ON u.major_id = m.id
       LEFT JOIN dorm_buildings db ON u.dorm_building_id = db.id
       LEFT JOIN user_roles ur ON u.role_id = ur.id
       ORDER BY u.created_at DESC`
    );

    res.json({
      success: true,
      data: {
        users: rows
      }
    });

  } catch (error) {
    console.error('获取用户列表错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};

// 创建用户
export const createUser = async (req, res) => {
  try {
    const { 
      name, 
      email, 
      password = 'password123', 
      role, 
      phone, 
      college, 
      major, 
      dorm_building_id, 
      room_number, 
      emergency_contact_name, 
      emergency_contact_phone 
    } = req.body;

    if (!name || !email || !role) {
      return res.status(400).json({
        success: false,
        message: '姓名、邮箱和角色都是必填项'
      });
    }

    // 检查邮箱是否已存在
    const [existingUsers] = await pool.execute(
      'SELECT id FROM users WHERE email = ?',
      [email]
    );

    if (existingUsers.length > 0) {
      return res.status(400).json({
        success: false,
        message: '该邮箱已被注册'
      });
    }

    // 查找角色ID
    let roleId = null;
    if (role) {
      const [roleRows] = await pool.execute('SELECT id FROM user_roles WHERE role_name = ?', [role]);
      roleId = roleRows.length > 0 ? roleRows[0].id : null;

      if (!roleId) {
        return res.status(400).json({
          success: false,
          message: '无效的角色'
        });
      }
    }

    // 查找学院和专业ID
    let collegeId = null, majorId = null;
    if (college) {
      const [collegeRows] = await pool.execute('SELECT id FROM colleges WHERE name = ?', [college]);
      collegeId = collegeRows.length > 0 ? collegeRows[0].id : null;
    }
    if (major) {
      const [majorRows] = await pool.execute('SELECT id FROM majors WHERE name = ?', [major]);
      majorId = majorRows.length > 0 ? majorRows[0].id : null;
    }

    // 加密密码
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    // 生成用户ID
    const userId = `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // 插入新用户
    await pool.execute(
      `INSERT INTO users (
        id, name, email, password, role_id, phone, college_id, major_id,
        dorm_building_id, room_number, emergency_contact_name, emergency_contact_phone
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        userId, name, email, hashedPassword, roleId, phone, collegeId, majorId,
        dorm_building_id, room_number, emergency_contact_name, emergency_contact_phone
      ]
    );

    res.status(201).json({
      success: true,
      message: '用户创建成功',
      data: {
        userId
      }
    });

  } catch (error) {
    console.error('创建用户错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};

// 更新用户
export const updateUser = async (req, res) => {
  try {
    const { id } = req.params;
    const { 
      name, 
      email, 
      role, 
      phone, 
      college, 
      major, 
      dorm_building_id, 
      room_number, 
      emergency_contact_name, 
      emergency_contact_phone 
    } = req.body;

    // 检查用户是否存在
    const [existingUsers] = await pool.execute('SELECT id FROM users WHERE id = ?', [id]);
    if (existingUsers.length === 0) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 查找角色ID
    let roleId = null;
    if (role) {
      const [roleRows] = await pool.execute('SELECT id FROM user_roles WHERE role_name = ?', [role]);
      roleId = roleRows.length > 0 ? roleRows[0].id : null;

      if (!roleId) {
        return res.status(400).json({
          success: false,
          message: '无效的角色'
        });
      }
    }

    // 查找学院和专业ID
    let collegeId = null, majorId = null;
    if (college) {
      const [collegeRows] = await pool.execute('SELECT id FROM colleges WHERE name = ?', [college]);
      collegeId = collegeRows.length > 0 ? collegeRows[0].id : null;
    }
    if (major) {
      const [majorRows] = await pool.execute('SELECT id FROM majors WHERE name = ?', [major]);
      majorId = majorRows.length > 0 ? majorRows[0].id : null;
    }

    // 更新用户信息
    await pool.execute(
      `UPDATE users SET
        name = ?, email = ?, role_id = ?, phone = ?, college_id = ?, major_id = ?,
        dorm_building_id = ?, room_number = ?, emergency_contact_name = ?, emergency_contact_phone = ?
       WHERE id = ?`,
      [
        name, email, roleId, phone, collegeId, majorId,
        dorm_building_id, room_number, emergency_contact_name, emergency_contact_phone, id
      ]
    );

    res.json({
      success: true,
      message: '用户更新成功'
    });

  } catch (error) {
    console.error('更新用户错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};

// 删除用户
export const deleteUser = async (req, res) => {
  try {
    const { id } = req.params;

    // 检查用户是否存在
    const [existingUsers] = await pool.execute('SELECT id FROM users WHERE id = ?', [id]);
    if (existingUsers.length === 0) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 删除用户
    await pool.execute('DELETE FROM users WHERE id = ?', [id]);

    res.json({
      success: true,
      message: '用户删除成功'
    });

  } catch (error) {
    console.error('删除用户错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};
