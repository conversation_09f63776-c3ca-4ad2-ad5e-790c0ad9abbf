-- 插入角色数据
INSERT INTO user_roles (id, role_name, role_description) VALUES
(1, '系统管理员', '系统管理员，拥有所有权限'),
(2, '宿舍管理员', '宿舍管理员，管理特定宿舍楼'),
(3, '学生', '学生用户'),
(4, '维修人员', '维修人员，处理维修请求')
ON DUPLICATE KEY UPDATE role_name = VALUES(role_name);

-- 插入学院数据
INSERT INTO colleges (id, name) VALUES
('college01', '计算机科学学院'),
('college02', '电子工程学院'),
('college03', '机械工程学院'),
('college04', '经济管理学院')
ON DUPLICATE KEY UPDATE name = VALUES(name);

-- 插入专业数据
INSERT INTO majors (id, name, college_id) VALUES
('major01', '计算机科学与技术', 'college01'),
('major02', '软件工程', 'college01'),
('major03', '电子信息工程', 'college02'),
('major04', '通信工程', 'college02')
ON DUPLICATE KEY UPDATE name = VALUES(name);

-- 插入宿舍楼数据
INSERT INTO dorm_buildings (id, name, floors, total_rooms) VALUES
('bldgA', 'A栋', 6, 120),
('bldgB', 'B栋', 8, 160),
('bldgC', 'C栋', 5, 100)
ON DUPLICATE KEY UPDATE name = VALUES(name);

-- 插入用户数据（密码是 password123 的bcrypt哈希）
INSERT INTO users (id, name, email, password, role_id, phone, college_id, major_id, dorm_building_id, emergency_contact_name, emergency_contact_phone) VALUES
('sysadmin01', '系统管理员', '<EMAIL>', '$2b$10$rOvHPGkwYvYFQVlXiXHxHOuBJKxj8qMxqGjZvQjZvQjZvQjZvQjZvO', 1, '13800000001', NULL, NULL, NULL, NULL, NULL),
('repair01', '爱德华·修理工', '<EMAIL>', '$2b$10$rOvHPGkwYvYFQVlXiXHxHOuBJKxj8qMxqGjZvQjZvQjZvQjZvQjZvO', 4, '13800000005', NULL, NULL, NULL, NULL, NULL),
('dormadmin01', '张三', '<EMAIL>', '$2b$10$rOvHPGkwYvYFQVlXiXHxHOuBJKxj8qMxqGjZvQjZvQjZvQjZvQjZvO', 2, '13800000002', NULL, NULL, 'bldgA', NULL, NULL),
('dormadmin02', '李四', '<EMAIL>', '$2b$10$rOvHPGkwYvYFQVlXiXHxHOuBJKxj8qMxqGjZvQjZvQjZvQjZvQjZvO', 2, '13800000003', NULL, NULL, 'bldgB', NULL, NULL),
('student01', '王五', '<EMAIL>', '$2b$10$rOvHPGkwYvYFQVlXiXHxHOuBJKxj8qMxqGjZvQjZvQjZvQjZvQjZvO', 3, '13800000004', 'college01', 'major01', 'bldgA', '王父', '13900000001'),
('student02', '赵六', '<EMAIL>', '$2b$10$rOvHPGkwYvYFQVlXiXHxHOuBJKxj8qMxqGjZvQjZvQjZvQjZvQjZvO', 3, '13800000006', 'college02', 'major03', 'bldgB', '赵母', '13900000002'),
('student03', '孙七', '<EMAIL>', '$2b$10$rOvHPGkwYvYFQVlXiXHxHOuBJKxj8qMxqGjZvQjZvQjZvQjZvQjZvO', 3, '13800000007', 'college01', 'major02', 'bldgA', '孙父', '13900000003')
ON DUPLICATE KEY UPDATE name = VALUES(name);

-- 更新宿舍楼的管理员分配
UPDATE dorm_buildings SET assigned_admin_id = 'dormadmin01' WHERE id = 'bldgA';
UPDATE dorm_buildings SET assigned_admin_id = 'dormadmin02' WHERE id = 'bldgB';

-- 插入维修请求数据
INSERT INTO repair_requests (id, student_id, room_number, dorm_building_id, description, status, assigned_staff_id, notes) VALUES
('repair001', 'student01', '101', 'bldgA', '水龙头漏水', '待处理', NULL, NULL),
('repair002', 'student02', '205', 'bldgB', '灯泡坏了', '已指派', 'repair01', '已安排维修人员'),
('repair003', 'student03', '102', 'bldgA', '门锁损坏', '已完成', 'repair01', '已更换新门锁')
ON DUPLICATE KEY UPDATE description = VALUES(description);
