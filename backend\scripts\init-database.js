import mysql from 'mysql2/promise';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const initDatabase = async () => {
  let connection;
  
  try {
    console.log('🔄 开始初始化数据库...');
    
    // 首先连接到MySQL服务器（不指定数据库）
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || 'root',
      charset: 'utf8mb4'
    });

    console.log('✅ 成功连接到MySQL服务器');

    // 读取并执行数据库结构SQL
    const schemaPath = path.join(__dirname, '../../database/dorm_management.sql');
    const schemaSQL = fs.readFileSync(schemaPath, 'utf8');
    
    // 分割SQL语句并执行
    const statements = schemaSQL.split(';').filter(stmt => stmt.trim().length > 0);
    
    for (const statement of statements) {
      if (statement.trim()) {
        await connection.execute(statement);
      }
    }
    
    console.log('✅ 数据库结构创建成功');

    // 读取并执行测试数据SQL
    const dataPath = path.join(__dirname, '../../database/insert_test_data.sql');
    const dataSQL = fs.readFileSync(dataPath, 'utf8');
    
    // 分割SQL语句并执行
    const dataStatements = dataSQL.split(';').filter(stmt => stmt.trim().length > 0);
    
    for (const statement of dataStatements) {
      if (statement.trim()) {
        await connection.execute(statement);
      }
    }
    
    console.log('✅ 测试数据插入成功');
    console.log('🎉 数据库初始化完成！');
    
  } catch (error) {
    console.error('❌ 数据库初始化失败:', error.message);
    console.error('错误详情:', error);

    if (error.code === 'ECONNREFUSED') {
      console.log('\n📋 MySQL连接被拒绝，请检查:');
      console.log('1. MySQL服务是否已启动');
      console.log('2. 连接配置是否正确:');
      console.log(`   - 主机: ${process.env.DB_HOST || 'localhost'}`);
      console.log(`   - 端口: ${process.env.DB_PORT || 3306}`);
      console.log(`   - 用户: ${process.env.DB_USER || 'root'}`);
      console.log(`   - 密码: ${process.env.DB_PASSWORD || 'root'}`);
      console.log('\n💡 如果您使用XAMPP，请确保MySQL服务已在XAMPP控制面板中启动');
      console.log('💡 如果您使用MySQL Workbench，请确保MySQL服务正在运行');
    } else if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.log('\n📋 访问被拒绝，请检查用户名和密码是否正确');
    }

    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
};

initDatabase();
