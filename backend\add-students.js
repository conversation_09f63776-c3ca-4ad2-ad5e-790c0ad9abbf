import { pool } from './config/database.js';

async function addStudents() {
  try {
    console.log('👥 添加学生测试数据...');
    
    // 添加更多学生
    const students = [
      { id: 'student03', name: '赵六', email: 'z<PERSON><PERSON><PERSON>@example.com' },
      { id: 'student04', name: '孙七', email: 'sun<PERSON>@example.com' },
      { id: 'student05', name: '周八', email: '<EMAIL>' },
      { id: 'student06', name: '吴九', email: '<EMAIL>' },
      { id: 'student07', name: '郑十', email: 'zhen<PERSON><EMAIL>' }
    ];
    
    for (const student of students) {
      try {
        await pool.execute(`
          INSERT INTO users (id, name, email, password_hash, role_name, created_at) 
          VALUES (?, ?, ?, '$2b$10$hashedpassword', '学生', NOW())
        `, [student.id, student.name, student.email]);
        console.log(`✅ 添加学生: ${student.name}`);
      } catch (error) {
        if (error.code === 'ER_DUP_ENTRY') {
          console.log(`⚠️ 学生已存在: ${student.name}`);
        } else {
          console.log(`❌ 添加失败: ${student.name} - ${error.message}`);
        }
      }
    }
    
    // 检查当前状态
    console.log('\n📊 当前状态:');
    const [userCount] = await pool.execute('SELECT COUNT(*) as count FROM users WHERE role_name = "学生"');
    console.log(`学生总数: ${userCount[0].count}`);
    
    const [allStudents] = await pool.execute('SELECT id, name, dorm_building_id, room_number FROM users WHERE role_name = "学生"');
    allStudents.forEach(s => {
      console.log(`  - ${s.name} (${s.id}) - 宿舍楼: ${s.dorm_building_id || '未分配'} - 房间: ${s.room_number || '未分配'}`);
    });
    
    await pool.end();
    console.log('\n🎉 学生数据添加完成！');
  } catch (error) {
    console.error('❌ 添加失败:', error);
    process.exit(1);
  }
}

addStudents();
