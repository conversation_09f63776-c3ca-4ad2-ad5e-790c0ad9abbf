# MySQL 安装和配置指南

## Windows 安装 MySQL

### 方法一：使用 MySQL Installer（推荐）

1. **下载 MySQL Installer**
   - 访问：https://dev.mysql.com/downloads/installer/
   - 下载 `mysql-installer-web-community-8.0.xx.x.msi`

2. **运行安装程序**
   - 双击下载的 `.msi` 文件
   - 选择 "Developer Default" 安装类型
   - 点击 "Next" 继续

3. **配置 MySQL Server**
   - 在 "Type and Networking" 页面，保持默认设置
   - 端口：3306
   - 点击 "Next"

4. **设置 Root 密码**
   - 选择 "Use Strong Password Encryption"
   - 设置 root 密码为：`root`
   - 确认密码：`root`
   - 点击 "Next"

5. **完成安装**
   - 点击 "Execute" 开始安装
   - 等待安装完成
   - 点击 "Finish"

### 方法二：使用 Chocolatey

如果您已安装 Chocolatey，可以使用以下命令：

```powershell
# 以管理员身份运行 PowerShell
choco install mysql

# 启动 MySQL 服务
net start mysql80
```

### 方法三：使用 XAMPP

1. 下载 XAMPP：https://www.apachefriends.org/download.html
2. 安装 XAMPP
3. 启动 XAMPP Control Panel
4. 启动 MySQL 服务

## 验证 MySQL 安装

1. **检查 MySQL 服务状态**
```powershell
# 检查服务是否运行
Get-Service -Name MySQL*

# 或者使用 net 命令
net start | findstr MySQL
```

2. **测试连接**
```powershell
# 使用命令行连接（如果 mysql 命令可用）
mysql -u root -p

# 输入密码：root
```

## 初始化数据库

安装完 MySQL 后，运行以下命令初始化数据库：

```bash
# 在项目的 backend 目录下运行
cd backend
npm run init-db
```

如果遇到连接错误，请检查：
1. MySQL 服务是否已启动
2. 用户名和密码是否正确（默认：root/root）
3. 端口是否正确（默认：3306）

## 常见问题解决

### 1. 端口被占用
如果 3306 端口被占用，可以：
- 修改 MySQL 配置使用其他端口
- 或者修改 `backend/.env` 文件中的 `DB_PORT`

### 2. 权限问题
如果遇到权限问题，可以尝试：
```sql
-- 连接到 MySQL 后执行
CREATE USER 'root'@'localhost' IDENTIFIED BY 'root';
GRANT ALL PRIVILEGES ON *.* TO 'root'@'localhost';
FLUSH PRIVILEGES;
```

### 3. 服务无法启动
- 检查是否有其他 MySQL 实例在运行
- 重启计算机后再试
- 检查 Windows 服务中的 MySQL 服务状态

## 手动创建数据库（备选方案）

如果自动初始化失败，可以手动执行：

1. **连接到 MySQL**
```bash
mysql -u root -p
```

2. **创建数据库**
```sql
CREATE DATABASE dorm_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE dorm_management;
```

3. **执行 SQL 文件**
```sql
-- 在 MySQL 命令行中执行
source D:/Student/vscode/database/dorm_management.sql;
source D:/Student/vscode/database/insert_test_data.sql;
```

## 验证数据库设置

执行以下查询验证数据是否正确插入：

```sql
USE dorm_management;

-- 检查表是否创建
SHOW TABLES;

-- 检查用户数据
SELECT id, name, email, role FROM users;

-- 检查学院数据
SELECT * FROM colleges;

-- 检查维修请求数据
SELECT * FROM repair_requests;
```

## 下一步

数据库配置完成后：

1. 确保后端服务器运行在 http://localhost:3002
2. 确保前端服务器运行在 http://localhost:5173
3. 使用测试账户登录系统

### 测试账户

- **系统管理员**: <EMAIL> / password123
- **宿舍管理员**: <EMAIL> / password123
- **学生**: <EMAIL> / password123
- **维修人员**: <EMAIL> / password123
