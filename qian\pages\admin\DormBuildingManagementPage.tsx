
import React, { useState, useEffect } from 'react';
import { DormBuilding, User, UserRole } from '../../types';
import { MOCK_USERS } from '../../constants';
import Table from '../../components/Table';
import Button from '../../components/Button';
import Modal from '../../components/Modal';
import Input from '../../components/Input';
import Card from '../../components/Card';

const API_BASE_URL = 'http://localhost:3002/api';

const DormBuildingManagementPage: React.FC = () => {
  const [buildings, setBuildings] = useState<DormBuilding[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingBuilding, setEditingBuilding] = useState<DormBuilding | null>(null);
  const [newBuilding, setNewBuilding] = useState<Partial<DormBuilding>>({});

  const dormAdmins = MOCK_USERS.filter(u => u.role === UserRole.DORM_ADMIN);

  // 获取宿舍楼列表
  const fetchBuildings = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_BASE_URL}/dorm-buildings`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error('获取宿舍楼列表失败');
      }

      const data = await response.json();
      if (data.success) {
        setBuildings(data.data || []);
      }
    } catch (error) {
      console.error('获取宿舍楼列表错误:', error);
      alert('获取宿舍楼列表失败，请重试');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchBuildings();
  }, []);

  const columns = [
    { header: '楼栋名称', accessor: 'name' as keyof DormBuilding },
    { header: '楼层数', accessor: 'floors' as keyof DormBuilding },
    { header: '总房间数', accessor: 'totalRooms' as keyof DormBuilding },
    { header: '分配的宿管', accessor: (item: DormBuilding) => dormAdmins.find(admin => admin.id === item.assignedAdminId)?.name || '未分配' },
    { 
      header: '操作', 
      accessor: 'id' as keyof DormBuilding,
      render: (building: DormBuilding) => (
        <div className="space-x-2">
          <Button size="sm" variant="ghost" onClick={() => handleEdit(building)}><i className="fas fa-edit"></i></Button>
          <Button size="sm" variant="danger" onClick={() => handleDelete(building.id)}><i className="fas fa-trash"></i></Button>
        </div>
      )
    }
  ];

  const handleEdit = (building: DormBuilding) => {
    setEditingBuilding(building);
    setNewBuilding(building);
    setIsModalOpen(true);
  };

  const handleDelete = async (buildingId: string) => {
    if (window.confirm('您确定要删除此宿舍楼吗？')) {
      try {
        const token = localStorage.getItem('token');
        const response = await fetch(`${API_BASE_URL}/dorm-buildings/${buildingId}`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });

        if (!response.ok) {
          throw new Error('删除宿舍楼失败');
        }

        const data = await response.json();
        if (data.success) {
          await fetchBuildings();
        }
      } catch (error) {
        console.error('删除宿舍楼错误:', error);
        alert('删除宿舍楼失败，请重试');
      }
    }
  };
  
  const handleOpenModal = (building: DormBuilding | null = null) => {
    setEditingBuilding(building);
    setNewBuilding(building ? { ...building } : { id: `bldg${Date.now()}`, name: '', floors: 0, totalRooms: 0 });
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setEditingBuilding(null);
    setNewBuilding({});
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setNewBuilding(prev => ({ ...prev, [name]: name === 'floors' || name === 'totalRooms' ? parseInt(value) : value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const token = localStorage.getItem('token');

      if (editingBuilding) {
        // 更新宿舍楼
        const response = await fetch(`${API_BASE_URL}/dorm-buildings/${editingBuilding.id}`, {
          method: 'PUT',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            name: newBuilding.name,
            floors: newBuilding.floors,
            total_rooms: newBuilding.totalRooms,
            assigned_admin_id: newBuilding.assignedAdminId
          }),
        });

        if (!response.ok) {
          throw new Error('更新宿舍楼失败');
        }

        const data = await response.json();
        if (data.success) {
          await fetchBuildings();
          handleCloseModal();
        }
      } else {
        // 创建新宿舍楼
        const response = await fetch(`${API_BASE_URL}/dorm-buildings`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            name: newBuilding.name,
            floors: newBuilding.floors,
            total_rooms: newBuilding.totalRooms,
            assigned_admin_id: newBuilding.assignedAdminId
          }),
        });

        if (!response.ok) {
          throw new Error('创建宿舍楼失败');
        }

        const data = await response.json();
        if (data.success) {
          await fetchBuildings();
          handleCloseModal();
        }
      }
    } catch (error) {
      console.error('保存宿舍楼错误:', error);
      alert('保存宿舍楼失败，请重试');
    }
  };

  if (isLoading) {
    return (
      <Card title="宿舍楼管理">
        <div className="flex justify-center items-center py-8">
          <div className="text-gray-500">加载中...</div>
        </div>
      </Card>
    );
  }

  return (
    <Card title="宿舍楼管理" actions={<Button onClick={() => handleOpenModal()} leftIcon={<i className="fas fa-plus mr-2"></i>}>添加宿舍楼</Button>}>
      <Table columns={columns} data={buildings} keyExtractor={(b) => b.id} />

      <Modal isOpen={isModalOpen} onClose={handleCloseModal} title={editingBuilding ? '编辑宿舍楼' : '添加新宿舍楼'}>
        <form onSubmit={handleSubmit} className="space-y-4">
          <Input name="name" label="楼栋名称" value={newBuilding.name || ''} onChange={handleInputChange} required />
          <Input name="floors" label="楼层数" type="number" value={newBuilding.floors || 0} onChange={handleInputChange} required />
          <Input name="totalRooms" label="总房间数" type="number" value={newBuilding.totalRooms || 0} onChange={handleInputChange} required />
          <div>
            <label htmlFor="assignedAdminId" className="block text-sm font-medium text-gray-700 mb-1">分配宿舍管理员 (可选)</label>
            <select
              id="assignedAdminId"
              name="assignedAdminId"
              value={newBuilding.assignedAdminId || ''}
              onChange={handleInputChange}
              className="block w-full px-3 py-2 border border-neutral-DEFAULT rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
            >
              <option value="">-- 选择管理员 --</option>
              {dormAdmins.map(admin => (
                <option key={admin.id} value={admin.id}>{admin.name}</option>
              ))}
            </select>
          </div>
          <div className="flex justify-end space-x-2 pt-4">
            <Button type="button" variant="ghost" onClick={handleCloseModal}>取消</Button>
            <Button type="submit">{editingBuilding ? '保存更改' : '添加宿舍楼'}</Button>
          </div>
        </form>
      </Modal>
    </Card>
  );
};

export default DormBuildingManagementPage;