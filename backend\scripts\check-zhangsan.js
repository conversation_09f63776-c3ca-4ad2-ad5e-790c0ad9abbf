import mysql from 'mysql2/promise';

const checkZhangsan = async () => {
  let connection;
  
  try {
    connection = await mysql.createConnection({
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: 'root',
      database: 'dorm_management',
      charset: 'utf8mb4'
    });

    console.log('🔍 检查张三的详细信息...\n');

    // 查找张三的详细信息
    const [users] = await connection.execute(`
      SELECT u.*, ur.role_name, db.name as building_name, db.id as building_id
      FROM users u 
      LEFT JOIN user_roles ur ON u.role_id = ur.id
      LEFT JOIN dorm_buildings db ON u.dorm_building_id = db.id
      WHERE u.name = '张三'
    `);

    if (users.length === 0) {
      console.log('❌ 没有找到张三');
      return;
    }

    const user = users[0];
    console.log('👤 张三的信息:');
    console.log(`   ID: ${user.id}`);
    console.log(`   姓名: ${user.name}`);
    console.log(`   邮箱: ${user.email}`);
    console.log(`   角色: ${user.role_name}`);
    console.log(`   分配的宿舍楼: ${user.building_name || '未分配'}`);
    console.log(`   宿舍楼ID: ${user.building_id || '无'}`);

    // 查看A栋的房间
    if (user.building_id) {
      console.log(`\n🏠 ${user.building_name}的房间列表:`);
      const [rooms] = await connection.execute(`
        SELECT * FROM rooms WHERE dorm_building_id = ? ORDER BY floor, room_number
      `, [user.building_id]);

      if (rooms.length > 0) {
        rooms.forEach((room, index) => {
          console.log(`   ${index + 1}. 房间${room.room_number} (${room.floor}楼, ${room.type}, 容量:${room.capacity}, 已住:${room.occupied_beds})`);
        });
      } else {
        console.log('   暂无房间');
      }

      // 查看床位信息
      console.log(`\n🛏️ ${user.building_name}的床位统计:`);
      const [bedStats] = await connection.execute(`
        SELECT 
          COUNT(*) as total_beds,
          SUM(CASE WHEN status = '已入住' THEN 1 ELSE 0 END) as occupied_beds,
          SUM(CASE WHEN status = '空闲' THEN 1 ELSE 0 END) as vacant_beds
        FROM beds b
        JOIN rooms r ON b.room_id = r.id
        WHERE r.dorm_building_id = ?
      `, [user.building_id]);

      if (bedStats.length > 0) {
        const stats = bedStats[0];
        console.log(`   总床位: ${stats.total_beds}`);
        console.log(`   已入住: ${stats.occupied_beds}`);
        console.log(`   空闲: ${stats.vacant_beds}`);
      }
    }

    // 检查所有宿舍楼的分配情况
    console.log('\n🏢 所有宿舍楼的管理员分配:');
    const [buildings] = await connection.execute(`
      SELECT db.*, u.name as admin_name, u.email as admin_email
      FROM dorm_buildings db
      LEFT JOIN users u ON db.assigned_admin_id = u.id
      ORDER BY db.created_at
    `);

    buildings.forEach((building, index) => {
      console.log(`   ${index + 1}. ${building.name} (ID: ${building.id})`);
      console.log(`      管理员: ${building.admin_name || '未分配'} (${building.admin_email || ''})`);
      console.log(`      管理员ID: ${building.assigned_admin_id || '无'}`);
      console.log('');
    });

  } catch (error) {
    console.error('❌ 检查失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
};

checkZhangsan();
