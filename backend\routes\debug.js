import express from 'express';
import { pool } from '../config/database.js';

const router = express.Router();

// 调试用户和宿舍楼分配
router.get('/user-building', async (req, res) => {
  try {
    // 查看所有宿舍管理员
    const [admins] = await pool.execute(`
      SELECT u.id, u.name, u.email, u.dorm_building_id, ur.role_name, db.name as building_name
      FROM users u 
      LEFT JOIN user_roles ur ON u.role_id = ur.id
      LEFT JOIN dorm_buildings db ON u.dorm_building_id = db.id
      WHERE ur.role_name = '宿舍管理员'
    `);

    // 查看所有宿舍楼
    const [buildings] = await pool.execute(`
      SELECT db.*, u.name as admin_name, u.email as admin_email
      FROM dorm_buildings db
      LEFT JOIN users u ON db.assigned_admin_id = u.id
    `);

    // 查看房间数量
    const [rooms] = await pool.execute(`
      SELECT COUNT(*) as count FROM rooms
    `);

    res.json({
      success: true,
      data: {
        admins,
        buildings,
        roomCount: rooms[0].count
      }
    });

  } catch (error) {
    console.error('调试查询错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误',
      error: error.message
    });
  }
});

// 修复张三的分配
router.post('/fix-zhangsan', async (req, res) => {
  try {
    // 找到张三
    const [zhangsan] = await pool.execute(`
      SELECT id FROM users WHERE name = '张三' OR email = '<EMAIL>'
    `);

    if (zhangsan.length === 0) {
      return res.json({
        success: false,
        message: '找不到张三的用户记录'
      });
    }

    const zhangsanId = zhangsan[0].id;

    // 找到A栋
    const [buildingA] = await pool.execute(`
      SELECT id FROM dorm_buildings WHERE name LIKE '%A%' OR id = 'bldgA'
    `);

    if (buildingA.length === 0) {
      return res.json({
        success: false,
        message: '找不到A栋记录'
      });
    }

    const buildingAId = buildingA[0].id;

    // 双向分配
    await pool.execute(`
      UPDATE users SET dorm_building_id = ? WHERE id = ?
    `, [buildingAId, zhangsanId]);

    await pool.execute(`
      UPDATE dorm_buildings SET assigned_admin_id = ? WHERE id = ?
    `, [zhangsanId, buildingAId]);

    // 验证结果
    const [verifyUser] = await pool.execute(`
      SELECT u.*, db.name as building_name
      FROM users u 
      LEFT JOIN dorm_buildings db ON u.dorm_building_id = db.id
      WHERE u.id = ?
    `, [zhangsanId]);

    res.json({
      success: true,
      message: '张三的宿舍楼分配已修复',
      data: {
        userId: zhangsanId,
        buildingId: buildingAId,
        userInfo: verifyUser[0]
      }
    });

  } catch (error) {
    console.error('修复张三分配错误:', error);
    res.status(500).json({
      success: false,
      message: '修复失败',
      error: error.message
    });
  }
});

export default router;
