import React, { useState, FormEvent } from 'react';
import { Major, College } from '../../types';
import { MOCK_MAJORS, MOCK_COLLEGES } from '../../constants';
import Table from '../../components/Table';
import Button from '../../components/Button';
import Modal from '../../components/Modal';
import Input from '../../components/Input';
import Card from '../../components/Card';

const MajorManagementPage: React.FC = () => {
  const [majors, setMajors] = useState<Major[]>(MOCK_MAJORS);
  const [colleges] = useState<College[]>(MOCK_COLLEGES); // For dropdown
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingMajor, setEditingMajor] = useState<Major | null>(null);
  const [newMajorData, setNewMajorData] = useState<Partial<Major>>({});

  const getCollegeName = (collegeId: string) => {
    return colleges.find(c => c.id === collegeId)?.name || '未知学院';
  };

  const columns = [
    { header: 'ID', accessor: 'id' as keyof Major },
    { header: '专业名称', accessor: 'name' as keyof Major },
    { header: '所属学院', accessor: (major: Major) => getCollegeName(major.collegeId) },
    {
      header: '操作',
      accessor: 'id' as keyof Major,
      render: (major: Major) => (
        <div className="space-x-2">
          <Button size="sm" variant="ghost" onClick={() => handleEdit(major)}><i className="fas fa-edit"></i></Button>
          <Button size="sm" variant="danger" onClick={() => handleDelete(major.id)}><i className="fas fa-trash"></i></Button>
        </div>
      ),
    },
  ];

  const handleOpenModal = (major: Major | null = null) => {
    setEditingMajor(major);
    setNewMajorData(major ? { ...major } : { id: '', name: '', collegeId: colleges[0]?.id || '' });
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setEditingMajor(null);
    setNewMajorData({});
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setNewMajorData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!newMajorData.name || !newMajorData.collegeId) {
        alert("专业名称和所属学院不能为空！");
        return;
    }

    if (editingMajor) {
      setMajors(majors.map(m => (m.id === editingMajor.id ? { ...newMajorData } as Major : m)));
    } else {
      setMajors([...majors, { ...newMajorData, id: `maj_${Date.now()}` } as Major]);
    }
    handleCloseModal();
  };

  const handleEdit = (major: Major) => {
    handleOpenModal(major);
  };

  const handleDelete = (majorId: string) => {
    if (window.confirm('您确定要删除此专业吗？')) {
      setMajors(majors.filter(m => m.id !== majorId));
    }
  };

  return (
    <Card title="专业管理" actions={<Button onClick={() => handleOpenModal()} leftIcon={<i className="fas fa-plus mr-2"></i>}>添加专业</Button>}>
      <Table columns={columns} data={majors} keyExtractor={major => major.id} />
      <Modal isOpen={isModalOpen} onClose={handleCloseModal} title={editingMajor ? '编辑专业' : '添加新专业'}>
        <form onSubmit={handleSubmit} className="space-y-4">
          <Input name="name" label="专业名称" value={newMajorData.name || ''} onChange={handleInputChange} required />
          <div>
            <label htmlFor="collegeId" className="block text-sm font-medium text-gray-700 mb-1">所属学院</label>
            <select
              id="collegeId"
              name="collegeId"
              value={newMajorData.collegeId || ''}
              onChange={handleInputChange}
              className="block w-full px-3 py-2 border border-neutral-DEFAULT rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
              required
            >
              <option value="" disabled>-- 选择学院 --</option>
              {colleges.map(college => (
                <option key={college.id} value={college.id}>{college.name}</option>
              ))}
            </select>
          </div>
          <div className="flex justify-end space-x-2 pt-4">
            <Button type="button" variant="ghost" onClick={handleCloseModal}>取消</Button>
            <Button type="submit">{editingMajor ? '保存更改' : '添加专业'}</Button>
          </div>
        </form>
      </Modal>
    </Card>
  );
};

export default MajorManagementPage;