# 易宿管 - 智能宿舍管理系统

一个基于 React + TypeScript + Node.js + MySQL 的现代化宿舍管理系统，提供完整的宿舍管理解决方案。

## 🚀 项目特色

- **多角色管理**: 系统管理员、宿舍管理员、学生、维修人员
- **完整功能**: 用户管理、宿舍分配、维修请求、公告管理、水电费管理等
- **现代化UI**: 响应式设计，美观的用户界面
- **实时数据**: 前后端分离，实时数据同步
- **安全认证**: JWT认证，角色权限控制

## 📋 系统功能

### 系统管理员
- 用户管理（增删改查）
- 学院、专业管理
- 宿舍楼管理
- 系统公告发布

### 宿舍管理员
- 房间管理
- 学生分配
- 违规记录
- 晚归记录
- 访客管理
- 文明宿舍评分

### 学生
- 个人信息管理
- 维修请求提交
- 水电费查询
- 公告查看

### 维修人员
- 维修任务处理
- 维修进度更新
- 维修记录查看

## 🛠️ 技术栈

### 前端
- React 19.1.0
- TypeScript
- React Router DOM
- Vite
- Tailwind CSS

### 后端
- Node.js
- Express.js
- MySQL 8.0
- JWT认证
- CORS支持

## 📦 项目结构

```
易宿管/
├── qian/                    # 前端项目
│   ├── components/          # 通用组件
│   ├── pages/              # 页面组件
│   ├── contexts/           # React上下文
│   ├── layouts/            # 布局组件
│   ├── types.ts            # TypeScript类型定义
│   └── constants.ts        # 常量定义
├── backend/                # 后端项目
│   ├── config/             # 配置文件
│   ├── controllers/        # 控制器
│   ├── middleware/         # 中间件
│   ├── routes/            # 路由
│   ├── scripts/           # 脚本文件
│   └── server.js          # 主服务器文件
├── database/              # 数据库文件
│   ├── dorm_management.sql # 数据库结构
│   └── insert_test_data.sql # 测试数据
└── README.md
```

## 🚀 快速开始

### 前置要求

- Node.js 18+ 
- MySQL 8.0+
- npm 或 yarn

### 1. 安装MySQL

#### Windows:
1. 下载MySQL安装程序: https://dev.mysql.com/downloads/mysql/
2. 运行安装程序，选择"Developer Default"
3. 设置root密码为 `root`（或修改 `backend/.env` 中的密码）
4. 完成安装后，确保MySQL服务已启动

#### macOS:
```bash
# 使用Homebrew安装
brew install mysql
brew services start mysql

# 设置root密码
mysql_secure_installation
```

#### Linux (Ubuntu/Debian):
```bash
sudo apt update
sudo apt install mysql-server
sudo systemctl start mysql
sudo mysql_secure_installation
```

### 2. 克隆并设置项目

```bash
# 进入项目目录
cd d:\Student\vscode

# 安装后端依赖
cd backend
npm install

# 安装前端依赖
cd ../qian
npm install
```

### 3. 配置数据库

```bash
# 回到后端目录
cd ../backend

# 初始化数据库（创建表和插入测试数据）
npm run init-db
```

### 4. 启动服务

```bash
# 启动后端服务器（在backend目录）
npm run dev

# 新开一个终端，启动前端服务器（在qian目录）
cd ../qian
npm run dev
```

### 5. 访问系统

- 前端地址: http://localhost:5173
- 后端API: http://localhost:3001
- 健康检查: http://localhost:3001/health

## 👥 测试账户

### 系统管理员
- 邮箱: `<EMAIL>`
- 密码: `password123`
- 角色: 系统管理员

### 宿舍管理员
- 邮箱: `<EMAIL>`
- 密码: `password123`
- 角色: 宿舍管理员 (A栋)

- 邮箱: `<EMAIL>`
- 密码: `password123`
- 角色: 宿舍管理员 (B栋)

### 学生
- 邮箱: `<EMAIL>`
- 密码: `password123`
- 角色: 学生 (A栋101)

- 邮箱: `<EMAIL>`
- 密码: `password123`
- 角色: 学生 (B栋205)

- 邮箱: `<EMAIL>`
- 密码: `password123`
- 角色: 学生 (A栋102)

### 维修人员
- 邮箱: `<EMAIL>`
- 密码: `password123`
- 角色: 维修人员

## 📚 API文档

### 主要API端点

#### 认证
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/register` - 用户注册

#### 用户管理
- `GET /api/users` - 获取用户列表 (系统管理员)
- `POST /api/users` - 创建用户 (系统管理员)
- `PUT /api/users/:id` - 更新用户 (系统管理员)
- `DELETE /api/users/:id` - 删除用户 (系统管理员)

#### 学院管理
- `GET /api/colleges` - 获取学院列表
- `POST /api/colleges` - 创建学院 (系统管理员)
- `PUT /api/colleges/:id` - 更新学院 (系统管理员)
- `DELETE /api/colleges/:id` - 删除学院 (系统管理员)

#### 维修请求
- `GET /api/repairs` - 获取维修请求列表
- `POST /api/repairs` - 创建维修请求 (学生、宿舍管理员)
- `PUT /api/repairs/:id/status` - 更新维修状态
- `DELETE /api/repairs/:id` - 删除维修请求

## 🔧 开发指南

### 环境变量配置

后端 `.env` 文件配置:
```env
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=root
DB_NAME=dorm_management

# JWT配置
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=24h

# 服务器配置
PORT=3001
NODE_ENV=development

# CORS配置
CORS_ORIGIN=http://localhost:5173
```

### 添加新的API端点

1. 在 `backend/controllers/` 创建控制器
2. 在 `backend/routes/` 创建路由
3. 在 `backend/server.js` 注册路由

### 添加新的前端页面

1. 在 `qian/pages/` 创建页面组件
2. 在 `qian/types.ts` 添加类型定义
3. 在 `App.tsx` 添加路由

## 🐛 故障排除

### 数据库连接问题
1. 确保MySQL服务已启动
2. 检查用户名和密码是否正确
3. 确认端口3306未被占用

### 前端无法连接后端
1. 确保后端服务器在端口3001运行
2. 检查CORS配置
3. 确认防火墙设置

### 权限问题
1. 确保使用正确的角色登录
2. 检查JWT令牌是否有效
3. 确认用户权限设置

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交Issue和Pull Request！

---

© 2024 易宿管 - 智能宿舍管理系统
