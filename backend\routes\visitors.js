import express from 'express';
import { 
  getVisitors, 
  createVisitor, 
  updateVisitor, 
  deleteVisitor, 
  getVisitorDetails,
  recordVisitorExit
} from '../controllers/visitorController.js';
import { authenticateToken } from '../middleware/auth.js';

const router = express.Router();

// 获取访客记录列表
router.get('/', authenticateToken, getVisitors);

// 获取访客记录详情
router.get('/:id', authenticateToken, getVisitorDetails);

// 创建访客记录
router.post('/', authenticateToken, createVisitor);

// 更新访客记录
router.put('/:id', authenticateToken, updateVisitor);

// 登记访客离开
router.put('/:id/exit', authenticateToken, recordVisitorExit);

// 删除访客记录
router.delete('/:id', authenticateToken, deleteVisitor);

export default router;
