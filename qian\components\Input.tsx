import React from 'react';

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  containerClassName?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
}

const Input: React.FC<InputProps> = ({ 
  label, 
  id, 
  error, 
  className = '', 
  containerClassName = '', 
  leftIcon,
  rightIcon,
  ...props 
}) => {
  const baseStyle = 'block w-full px-4 py-3 border border-gray-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 sm:text-sm transition-all duration-300 ease-in-out bg-white';
  const errorStyle = error ? 'border-red-500 focus:ring-red-500 focus:border-red-500' : '';
  const iconPadding = leftIcon ? 'pl-12' : rightIcon ? 'pr-12' : '';

  return (
    <div className={`mb-4 ${containerClassName}`}>
      {label && (
        <label htmlFor={id} className="block text-sm font-semibold text-gray-700 mb-2 flex items-center">
          {label}
        </label>
      )}
      <div className="relative">
        {leftIcon && (
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <div className="text-gray-400 text-lg">
              {leftIcon}
            </div>
          </div>
        )}
        <input
          id={id}
          className={`${baseStyle} ${errorStyle} ${iconPadding} ${className} input-focus`}
          {...props}
        />
        {rightIcon && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
            <div className="text-gray-400 text-lg">
              {rightIcon}
            </div>
          </div>
        )}
      </div>
      {error && (
        <div className="mt-2 flex items-center text-red-600 text-sm animate-fade-in">
          <i className="fas fa-exclamation-circle mr-2"></i>
          {error}
        </div>
      )}
    </div>
  );
};

export default Input;