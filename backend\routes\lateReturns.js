import express from 'express';
import { 
  getLateReturns, 
  createLateReturn, 
  updateLateReturn, 
  deleteLateReturn, 
  getLateReturnDetails 
} from '../controllers/lateReturnController.js';
import { authenticateToken } from '../middleware/auth.js';

const router = express.Router();

// 获取晚归记录列表
router.get('/', authenticateToken, getLateReturns);

// 获取晚归记录详情
router.get('/:id', authenticateToken, getLateReturnDetails);

// 创建晚归记录
router.post('/', authenticateToken, createLateReturn);

// 更新晚归记录
router.put('/:id', authenticateToken, updateLateReturn);

// 删除晚归记录
router.delete('/:id', authenticateToken, deleteLateReturn);

export default router;
