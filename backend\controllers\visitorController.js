import { pool } from '../config/database.js';

// 获取访客记录列表（支持按宿舍楼筛选）
export const getVisitors = async (req, res) => {
  try {
    const { building_id } = req.query;
    
    console.log('获取访客记录请求:', { building_id, user: req.user?.id });
    
    let query = `
      SELECT v.*, u.name as visited_student_name, u.room_number, db.name as building_name
      FROM visitors v
      LEFT JOIN users u ON v.visited_student_id = u.id
      LEFT JOIN dorm_buildings db ON v.dorm_building_id = db.id
    `;
    let params = [];

    if (building_id) {
      query += ' WHERE v.dorm_building_id = ?';
      params.push(building_id);
    }

    query += ' ORDER BY v.entry_time DESC';

    console.log('查询SQL:', query);
    console.log('查询参数:', params);

    const [visitors] = await pool.execute(query, params);

    console.log(`查询结果: ${visitors.length} 条访客记录`);
    visitors.forEach(v => {
      console.log(`  - ${v.visitor_name} 访问 ${v.visited_student_name} - 进入: ${v.entry_time} 离开: ${v.exit_time || '未离开'}`);
    });

    res.json({
      success: true,
      data: visitors
    });

  } catch (error) {
    console.error('获取访客记录错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};

// 获取访客记录详情
export const getVisitorDetails = async (req, res) => {
  try {
    const { id } = req.params;
    
    const [visitors] = await pool.execute(`
      SELECT v.*, u.name as visited_student_name, u.room_number, db.name as building_name
      FROM visitors v
      LEFT JOIN users u ON v.visited_student_id = u.id
      LEFT JOIN dorm_buildings db ON v.dorm_building_id = db.id
      WHERE v.id = ?
    `, [id]);

    if (visitors.length === 0) {
      return res.status(404).json({
        success: false,
        message: '访客记录不存在'
      });
    }

    res.json({
      success: true,
      data: visitors[0]
    });

  } catch (error) {
    console.error('获取访客记录详情错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};

// 创建访客记录
export const createVisitor = async (req, res) => {
  try {
    const { 
      visitor_name, 
      visitor_id_number, 
      reason, 
      entry_time, 
      exit_time, 
      visited_student_id, 
      dorm_building_id 
    } = req.body;
    const recorded_by = req.user.id;
    
    // 生成唯一ID
    const visitorId = `vis_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    console.log('创建访客记录参数:', {
      visitorId,
      visitor_name,
      visitor_id_number,
      reason,
      entry_time,
      exit_time,
      visited_student_id,
      dorm_building_id,
      recorded_by
    });

    // 验证被访学生是否存在
    const [students] = await pool.execute(
      'SELECT id, name FROM users WHERE id = ? AND role_id = 3',
      [visited_student_id]
    );

    if (students.length === 0) {
      return res.status(400).json({
        success: false,
        message: '被访学生不存在'
      });
    }

    // 插入访客记录
    await pool.execute(`
      INSERT INTO visitors (
        id, visitor_name, visitor_id_number, reason, entry_time, exit_time, 
        visited_student_id, dorm_building_id, recorded_by
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      visitorId, visitor_name, visitor_id_number, reason, entry_time, 
      exit_time, visited_student_id, dorm_building_id, recorded_by
    ]);

    console.log('✅ 访客记录创建成功:', visitorId);

    res.status(201).json({
      success: true,
      message: '访客记录创建成功',
      data: { id: visitorId }
    });

  } catch (error) {
    console.error('创建访客记录错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};

// 更新访客记录
export const updateVisitor = async (req, res) => {
  try {
    const { id } = req.params;
    const { 
      visitor_name, 
      visitor_id_number, 
      reason, 
      entry_time, 
      exit_time, 
      visited_student_id, 
      dorm_building_id 
    } = req.body;
    
    // 检查记录是否存在
    const [existing] = await pool.execute(
      'SELECT id FROM visitors WHERE id = ?',
      [id]
    );

    if (existing.length === 0) {
      return res.status(404).json({
        success: false,
        message: '访客记录不存在'
      });
    }

    // 更新记录
    await pool.execute(`
      UPDATE visitors 
      SET visitor_name = ?, visitor_id_number = ?, reason = ?, entry_time = ?, 
          exit_time = ?, visited_student_id = ?, dorm_building_id = ?
      WHERE id = ?
    `, [
      visitor_name, visitor_id_number, reason, entry_time, 
      exit_time, visited_student_id, dorm_building_id, id
    ]);

    console.log('✅ 访客记录更新成功:', id);

    res.json({
      success: true,
      message: '访客记录更新成功'
    });

  } catch (error) {
    console.error('更新访客记录错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};

// 登记访客离开
export const recordVisitorExit = async (req, res) => {
  try {
    const { id } = req.params;
    const { exit_time } = req.body;
    
    // 检查记录是否存在
    const [existing] = await pool.execute(
      'SELECT id, exit_time FROM visitors WHERE id = ?',
      [id]
    );

    if (existing.length === 0) {
      return res.status(404).json({
        success: false,
        message: '访客记录不存在'
      });
    }

    if (existing[0].exit_time) {
      return res.status(400).json({
        success: false,
        message: '访客已经登记离开'
      });
    }

    // 更新离开时间
    await pool.execute(
      'UPDATE visitors SET exit_time = ? WHERE id = ?',
      [exit_time || new Date(), id]
    );

    console.log('✅ 访客离开登记成功:', id);

    res.json({
      success: true,
      message: '访客离开登记成功'
    });

  } catch (error) {
    console.error('登记访客离开错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};

// 删除访客记录
export const deleteVisitor = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 检查记录是否存在
    const [existing] = await pool.execute(
      'SELECT id FROM visitors WHERE id = ?',
      [id]
    );

    if (existing.length === 0) {
      return res.status(404).json({
        success: false,
        message: '访客记录不存在'
      });
    }

    // 删除记录
    await pool.execute('DELETE FROM visitors WHERE id = ?', [id]);

    console.log('✅ 访客记录删除成功:', id);

    res.json({
      success: true,
      message: '访客记录删除成功'
    });

  } catch (error) {
    console.error('删除访客记录错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};
