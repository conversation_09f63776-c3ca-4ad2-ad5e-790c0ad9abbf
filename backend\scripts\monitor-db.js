import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

dotenv.config();

const monitorDB = async () => {
  let connection;
  
  try {
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || 'root',
      database: process.env.DB_NAME || 'dorm_management',
      charset: 'utf8mb4'
    });

    console.log('🔍 开始监控数据库变化...');
    console.log('按 Ctrl+C 停止监控\n');

    let lastCounts = {};

    const checkChanges = async () => {
      try {
        // 获取各表的记录数
        const [userCount] = await connection.execute('SELECT COUNT(*) as count FROM users');
        const [collegeCount] = await connection.execute('SELECT COUNT(*) as count FROM colleges');
        const [majorCount] = await connection.execute('SELECT COUNT(*) as count FROM majors');
        const [repairCount] = await connection.execute('SELECT COUNT(*) as count FROM repair_requests');

        const currentCounts = {
          users: userCount[0].count,
          colleges: collegeCount[0].count,
          majors: majorCount[0].count,
          repairs: repairCount[0].count
        };

        // 检查是否有变化
        let hasChanges = false;
        for (const [table, count] of Object.entries(currentCounts)) {
          if (lastCounts[table] !== undefined && lastCounts[table] !== count) {
            console.log(`🔄 ${new Date().toLocaleTimeString()} - ${table} 表变化: ${lastCounts[table]} → ${count}`);
            hasChanges = true;
          }
        }

        if (hasChanges) {
          // 显示最新的数据
          console.log('\n📋 最新数据:');
          
          // 最新用户
          const [latestUsers] = await connection.execute(`
            SELECT u.name, u.email, ur.role_name, u.created_at
            FROM users u 
            LEFT JOIN user_roles ur ON u.role_id = ur.id 
            ORDER BY u.created_at DESC LIMIT 3
          `);
          
          if (latestUsers.length > 0) {
            console.log('   最新用户:');
            latestUsers.forEach(user => {
              console.log(`     - ${user.name} (${user.email}) - ${user.role_name} - ${user.created_at}`);
            });
          }

          // 最新学院
          const [latestColleges] = await connection.execute(`
            SELECT name, created_at FROM colleges ORDER BY created_at DESC LIMIT 3
          `);
          
          if (latestColleges.length > 0) {
            console.log('   最新学院:');
            latestColleges.forEach(college => {
              console.log(`     - ${college.name} - ${college.created_at}`);
            });
          }

          // 最新维修请求
          const [latestRepairs] = await connection.execute(`
            SELECT rr.description, rr.status, u.name as student_name, rr.created_at
            FROM repair_requests rr 
            LEFT JOIN users u ON rr.student_id = u.id 
            ORDER BY rr.created_at DESC LIMIT 3
          `);
          
          if (latestRepairs.length > 0) {
            console.log('   最新维修请求:');
            latestRepairs.forEach(repair => {
              console.log(`     - ${repair.description} (${repair.status}) - ${repair.student_name} - ${repair.created_at}`);
            });
          }

          console.log('\n' + '='.repeat(60) + '\n');
        }

        lastCounts = currentCounts;

        // 显示当前统计（每10次检查显示一次）
        if (Math.random() < 0.1) {
          console.log(`📊 ${new Date().toLocaleTimeString()} - 当前统计: 用户:${currentCounts.users}, 学院:${currentCounts.colleges}, 专业:${currentCounts.majors}, 维修:${currentCounts.repairs}`);
        }

      } catch (error) {
        console.error('❌ 监控错误:', error.message);
      }
    };

    // 初始检查
    await checkChanges();

    // 每2秒检查一次
    const interval = setInterval(checkChanges, 2000);

    // 处理退出信号
    process.on('SIGINT', () => {
      console.log('\n🛑 停止监控...');
      clearInterval(interval);
      if (connection) {
        connection.end();
      }
      process.exit(0);
    });

  } catch (error) {
    console.error('❌ 连接数据库失败:', error.message);
    process.exit(1);
  }
};

monitorDB();
