import { pool } from './config/database.js';

async function insertStudentData() {
  try {
    console.log('🏠 插入学生分配测试数据...');
    
    // 1. 首先检查当前数据状态
    console.log('\n📊 检查当前数据状态:');
    
    const [users] = await pool.execute('SELECT id, name, role_name FROM users WHERE role_name = "学生"');
    console.log(`学生用户数量: ${users.length}`);
    users.forEach(u => console.log(`  - ${u.name} (${u.id})`));
    
    const [rooms] = await pool.execute('SELECT id, room_number, dorm_building_id, capacity, occupied_beds FROM rooms');
    console.log(`\n房间数量: ${rooms.length}`);
    rooms.forEach(r => console.log(`  - 房间${r.room_number} (${r.occupied_beds}/${r.capacity})`));
    
    const [beds] = await pool.execute('SELECT id, bed_number, room_id, status, student_id FROM beds');
    console.log(`\n床位数量: ${beds.length}`);
    const occupiedBeds = beds.filter(b => b.status === '已入住').length;
    console.log(`已入住床位: ${occupiedBeds}/${beds.length}`);
    
    // 2. 添加更多学生用户
    console.log('\n👥 添加更多学生用户...');
    const newStudents = [
      { id: 'student03', name: '赵六', email: '<EMAIL>' },
      { id: 'student04', name: '孙七', email: '<EMAIL>' },
      { id: 'student05', name: '周八', email: '<EMAIL>' },
      { id: 'student06', name: '吴九', email: '<EMAIL>' },
      { id: 'student07', name: '郑十', email: '<EMAIL>' },
      { id: 'student08', name: '王十一', email: '<EMAIL>' }
    ];
    
    for (const student of newStudents) {
      try {
        await pool.execute(`
          INSERT INTO users (id, name, email, password_hash, role_name, created_at) 
          VALUES (?, ?, ?, ?, '学生', NOW())
        `, [student.id, student.name, student.email, '$2b$10$hashedpassword']);
        console.log(`  ✅ 添加学生: ${student.name}`);
      } catch (error) {
        if (error.code === 'ER_DUP_ENTRY') {
          console.log(`  ⚠️ 学生已存在: ${student.name}`);
        } else {
          console.log(`  ❌ 添加失败: ${student.name} - ${error.message}`);
        }
      }
    }
    
    // 3. 添加更多房间（如果需要）
    console.log('\n🏠 检查并添加更多房间...');
    const [currentRooms] = await pool.execute('SELECT COUNT(*) as count FROM rooms WHERE dorm_building_id = "bldgA"');
    if (currentRooms[0].count < 5) {
      const newRooms = [
        { id: 'room_102', room_number: '102', capacity: 4 },
        { id: 'room_103', room_number: '103', capacity: 4 },
        { id: 'room_104', room_number: '104', capacity: 4 },
        { id: 'room_201', room_number: '201', capacity: 4 },
        { id: 'room_202', room_number: '202', capacity: 4 }
      ];
      
      for (const room of newRooms) {
        try {
          await pool.execute(`
            INSERT INTO rooms (id, room_number, dorm_building_id, floor, capacity, occupied_beds) 
            VALUES (?, ?, 'bldgA', ?, ?, 0)
          `, [room.id, room.room_number, Math.floor(parseInt(room.room_number) / 100), room.capacity]);
          
          // 为每个房间创建床位
          for (let i = 1; i <= room.capacity; i++) {
            const bedId = `${room.id}_bed${i}`;
            await pool.execute(`
              INSERT INTO beds (id, bed_number, room_id, status) 
              VALUES (?, ?, ?, '空闲')
            `, [bedId, i, room.id]);
          }
          
          console.log(`  ✅ 添加房间: ${room.room_number} (${room.capacity}个床位)`);
        } catch (error) {
          if (error.code === 'ER_DUP_ENTRY') {
            console.log(`  ⚠️ 房间已存在: ${room.room_number}`);
          } else {
            console.log(`  ❌ 添加失败: ${room.room_number} - ${error.message}`);
          }
        }
      }
    }
    
    // 4. 分配一些学生到床位
    console.log('\n🛏️ 分配学生到床位...');
    const [availableStudents] = await pool.execute(`
      SELECT u.id, u.name 
      FROM users u 
      LEFT JOIN beds b ON u.id = b.student_id 
      WHERE u.role_name = '学生' AND b.student_id IS NULL
      LIMIT 6
    `);
    
    const [availableBeds] = await pool.execute(`
      SELECT b.id, b.bed_number, r.room_number 
      FROM beds b 
      JOIN rooms r ON b.room_id = r.id 
      WHERE b.status = '空闲' 
      LIMIT 6
    `);
    
    console.log(`可分配学生: ${availableStudents.length}人`);
    console.log(`可用床位: ${availableBeds.length}个`);
    
    const assignmentCount = Math.min(availableStudents.length, availableBeds.length);
    for (let i = 0; i < assignmentCount; i++) {
      const student = availableStudents[i];
      const bed = availableBeds[i];
      
      try {
        // 分配床位
        await pool.execute(`
          UPDATE beds SET status = '已入住', student_id = ? WHERE id = ?
        `, [student.id, bed.id]);
        
        // 更新学生房间信息
        await pool.execute(`
          UPDATE users SET room_number = ? WHERE id = ?
        `, [bed.room_number, student.id]);
        
        // 更新房间已住人数
        await pool.execute(`
          UPDATE rooms SET occupied_beds = (
            SELECT COUNT(*) FROM beds WHERE room_id = (
              SELECT room_id FROM beds WHERE id = ?
            ) AND status = '已入住'
          ) WHERE id = (
            SELECT room_id FROM beds WHERE id = ?
          )
        `, [bed.id, bed.id]);
        
        console.log(`  ✅ ${student.name} -> 房间${bed.room_number} 床位${bed.bed_number}`);
      } catch (error) {
        console.log(`  ❌ 分配失败: ${student.name} - ${error.message}`);
      }
    }
    
    // 5. 显示最终统计
    console.log('\n📊 最终统计:');
    const [finalUsers] = await pool.execute('SELECT COUNT(*) as count FROM users WHERE role_name = "学生"');
    const [finalRooms] = await pool.execute('SELECT COUNT(*) as count FROM rooms');
    const [finalBeds] = await pool.execute('SELECT COUNT(*) as count FROM beds');
    const [occupiedBedsCount] = await pool.execute('SELECT COUNT(*) as count FROM beds WHERE status = "已入住"');
    
    console.log(`学生总数: ${finalUsers[0].count}`);
    console.log(`房间总数: ${finalRooms[0].count}`);
    console.log(`床位总数: ${finalBeds[0].count}`);
    console.log(`已入住床位: ${occupiedBedsCount[0].count}`);
    console.log(`空闲床位: ${finalBeds[0].count - occupiedBedsCount[0].count}`);
    
    await pool.end();
    console.log('\n🎉 学生分配测试数据插入完成！');
  } catch (error) {
    console.error('❌ 插入数据失败:', error);
    process.exit(1);
  }
}

insertStudentData();
