import React from 'react';
import { useAuth } from '../../contexts/AuthContext';
import { UtilityBill } from '../../types';
import { MOCK_UTILITY_BILLS } from '../../constants';
import Table from '../../components/Table';
import Card from '../../components/Card';
import Button from '../../components/Button';

const UtilityBillsPage: React.FC = () => {
  const { currentUser } = useAuth();

  if (!currentUser) {
    return <p>正在加载用户信息...</p>;
  }

  const studentBills = MOCK_UTILITY_BILLS.filter(bill => bill.studentId === currentUser.id)
    .sort((a, b) => b.month.localeCompare(a.month)); // Sort by month, descending

  const unpaidBills = studentBills.filter(bill => !bill.isPaid);
  const totalDue = unpaidBills.reduce((sum, bill) => sum + bill.totalCost, 0);

  const columns = [
    { header: '账单月份', accessor: 'month' as keyof UtilityBill },
    { header: '电费 (元)', accessor: (bill: UtilityBill) => bill.electricityCost.toFixed(2) },
    { header: '水费 (元)', accessor: (bill: UtilityBill) => bill.waterCost.toFixed(2) },
    { header: '总计 (元)', accessor: (bill: UtilityBill) => bill.totalCost.toFixed(2) },
    { 
      header: '支付状态', 
      accessor: 'isPaid' as keyof UtilityBill,
      render: (bill: UtilityBill) => (
        <span className={`px-2 py-1 text-xs font-semibold rounded-full ${bill.isPaid ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'}`}>
          {bill.isPaid ? '已支付' : '未支付'}
        </span>
      )
    },
    {
        header: '操作',
        accessor: 'id' as keyof UtilityBill,
        render: (bill: UtilityBill) => (
            !bill.isPaid ? (
                <Button size="sm" onClick={() => alert(`模拟支付账单 ${bill.month}，金额 ${bill.totalCost.toFixed(2)} 元`)}>
                    立即支付
                </Button>
            ) : null
        )
    }
  ];

  return (
    <div className="space-y-6">
      <h1 className="text-3xl font-semibold text-neutral-dark">水电费账单</h1>

      {unpaidBills.length > 0 && (
        <Card title="待缴费用提醒" className="border-l-4 border-red-500">
          <div className="flex justify-between items-center">
            <div>
              <p className="text-lg font-semibold text-red-600">您当前有未缴清的水电费。</p>
              <p className="text-2xl font-bold text-red-700">总计欠费: {totalDue.toFixed(2)} 元</p>
            </div>
            <Button variant="danger" onClick={() => alert(`模拟支付全部欠费 ${totalDue.toFixed(2)} 元`)}>
                一键支付全部
            </Button>
          </div>
        </Card>
      )}

      <Card title="账单明细">
        <Table
          columns={columns}
          data={studentBills}
          keyExtractor={bill => bill.id}
          emptyStateMessage="您目前没有水电费账单记录。"
        />
      </Card>
    </div>
  );
};

export default UtilityBillsPage;