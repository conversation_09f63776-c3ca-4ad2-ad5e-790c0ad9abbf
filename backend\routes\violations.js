import express from 'express';
import { 
  getViolations, 
  createViolation, 
  updateViolation, 
  deleteViolation, 
  getViolationDetails 
} from '../controllers/violationController.js';
import { authenticateToken } from '../middleware/auth.js';

const router = express.Router();

// 获取违规记录列表
router.get('/', authenticateToken, getViolations);

// 获取违规记录详情
router.get('/:id', authenticateToken, getViolationDetails);

// 创建违规记录
router.post('/', authenticateToken, createViolation);

// 更新违规记录
router.put('/:id', authenticateToken, updateViolation);

// 删除违规记录
router.delete('/:id', authenticateToken, deleteViolation);

export default router;
