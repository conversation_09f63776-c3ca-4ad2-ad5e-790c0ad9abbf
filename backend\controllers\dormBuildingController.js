import { pool } from '../config/database.js';

// 获取所有宿舍楼
export const getDormBuildings = async (req, res) => {
  try {
    const [buildings] = await pool.execute(`
      SELECT db.*, u.name as admin_name
      FROM dorm_buildings db
      LEFT JOIN users u ON db.assigned_admin_id = u.id
      ORDER BY db.created_at DESC
    `);

    res.json({
      success: true,
      data: buildings
    });

  } catch (error) {
    console.error('获取宿舍楼列表错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};

// 创建宿舍楼
export const createDormBuilding = async (req, res) => {
  try {
    const { 
      name, 
      floors, 
      total_rooms, 
      assigned_admin_id 
    } = req.body;

    if (!name || !floors || !total_rooms) {
      return res.status(400).json({
        success: false,
        message: '宿舍楼名称、楼层数和房间总数都是必填项'
      });
    }

    // 检查宿舍楼名称是否已存在
    const [existingBuildings] = await pool.execute(
      'SELECT id FROM dorm_buildings WHERE name = ?',
      [name]
    );

    if (existingBuildings.length > 0) {
      return res.status(400).json({
        success: false,
        message: '该宿舍楼名称已存在'
      });
    }

    // 生成宿舍楼ID
    const buildingId = `bldg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // 插入新宿舍楼
    await pool.execute(
      `INSERT INTO dorm_buildings (
        id, name, floors, total_rooms, assigned_admin_id
      ) VALUES (?, ?, ?, ?, ?)`,
      [
        buildingId, name, floors, total_rooms, assigned_admin_id || null
      ]
    );

    res.status(201).json({
      success: true,
      message: '宿舍楼创建成功',
      data: {
        buildingId
      }
    });

  } catch (error) {
    console.error('创建宿舍楼错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};

// 更新宿舍楼
export const updateDormBuilding = async (req, res) => {
  try {
    const { id } = req.params;
    const { 
      name, 
      floors, 
      total_rooms, 
      assigned_admin_id 
    } = req.body;

    // 检查宿舍楼是否存在
    const [existingBuildings] = await pool.execute('SELECT id FROM dorm_buildings WHERE id = ?', [id]);
    if (existingBuildings.length === 0) {
      return res.status(404).json({
        success: false,
        message: '宿舍楼不存在'
      });
    }

    // 更新宿舍楼信息
    await pool.execute(
      `UPDATE dorm_buildings SET 
        name = ?, floors = ?, total_rooms = ?, assigned_admin_id = ?
       WHERE id = ?`,
      [
        name, floors, total_rooms, assigned_admin_id || null, id
      ]
    );

    res.json({
      success: true,
      message: '宿舍楼更新成功'
    });

  } catch (error) {
    console.error('更新宿舍楼错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};

// 删除宿舍楼
export const deleteDormBuilding = async (req, res) => {
  try {
    const { id } = req.params;

    // 检查宿舍楼是否存在
    const [existingBuildings] = await pool.execute('SELECT id FROM dorm_buildings WHERE id = ?', [id]);
    if (existingBuildings.length === 0) {
      return res.status(404).json({
        success: false,
        message: '宿舍楼不存在'
      });
    }

    // 检查是否有用户关联到这个宿舍楼
    const [relatedUsers] = await pool.execute('SELECT id FROM users WHERE dorm_building_id = ?', [id]);
    if (relatedUsers.length > 0) {
      return res.status(400).json({
        success: false,
        message: '无法删除宿舍楼，还有用户关联到此宿舍楼'
      });
    }

    // 删除宿舍楼
    await pool.execute('DELETE FROM dorm_buildings WHERE id = ?', [id]);

    res.json({
      success: true,
      message: '宿舍楼删除成功'
    });

  } catch (error) {
    console.error('删除宿舍楼错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};
