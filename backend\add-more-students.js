import { pool } from './config/database.js';
import bcrypt from 'bcrypt';

async function addMoreStudents() {
  try {
    console.log('👥 添加更多学生数据...');
    
    // 生成密码哈希
    const passwordHash = await bcrypt.hash('password123', 10);
    
    // 添加更多学生
    const students = [
      { id: 'student03', name: '赵六', email: 'z<PERSON><PERSON><PERSON>@example.com', building: 'bldgA' },
      { id: 'student04', name: '孙七', email: '<EMAIL>', building: 'bldgA' },
      { id: 'student05', name: '周八', email: '<EMAIL>', building: 'bldgA' },
      { id: 'student06', name: '吴九', email: '<EMAIL>', building: 'bldgA' },
      { id: 'student07', name: '郑十', email: '<EMAIL>', building: 'bldgB' },
      { id: 'student08', name: '王十一', email: 'wang<PERSON><PERSON>@example.com', building: 'bldgB' }
    ];
    
    for (const student of students) {
      try {
        await pool.execute(`
          INSERT INTO users (id, name, email, password, role_id, phone, college_id, major_id, dorm_building_id, emergency_contact_name, emergency_contact_phone) 
          VALUES (?, ?, ?, ?, 3, ?, 'college01', 'major01', ?, ?, ?)
        `, [
          student.id, 
          student.name, 
          student.email, 
          passwordHash,
          '138000000' + (Math.floor(Math.random() * 90) + 10), // 随机手机号
          student.building,
          student.name.slice(0, 1) + '父', // 紧急联系人
          '139000000' + (Math.floor(Math.random() * 90) + 10) // 紧急联系人电话
        ]);
        console.log(`✅ 添加学生: ${student.name} -> ${student.building}`);
      } catch (error) {
        if (error.code === 'ER_DUP_ENTRY') {
          console.log(`⚠️ 学生已存在: ${student.name}`);
        } else {
          console.log(`❌ 添加失败: ${student.name} - ${error.message}`);
        }
      }
    }
    
    // 检查当前状态
    console.log('\n📊 当前学生状态:');
    const [allStudents] = await pool.execute(`
      SELECT u.id, u.name, u.dorm_building_id, u.room_number, db.name as building_name
      FROM users u
      LEFT JOIN dorm_buildings db ON u.dorm_building_id = db.id
      WHERE u.role_id = 3
      ORDER BY u.dorm_building_id, u.name
    `);
    
    console.log(`学生总数: ${allStudents.length}`);
    
    const buildingGroups = {};
    allStudents.forEach(s => {
      const building = s.building_name || '未分配';
      if (!buildingGroups[building]) buildingGroups[building] = [];
      buildingGroups[building].push(s);
    });
    
    Object.keys(buildingGroups).forEach(building => {
      console.log(`\n${building}:`);
      buildingGroups[building].forEach(s => {
        console.log(`  - ${s.name} (${s.id}) - 房间: ${s.room_number || '未分配'}`);
      });
    });
    
    await pool.end();
    console.log('\n🎉 学生数据添加完成！');
  } catch (error) {
    console.error('❌ 添加失败:', error);
    process.exit(1);
  }
}

addMoreStudents();
