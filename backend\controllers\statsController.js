import { pool } from '../config/database.js';

// 获取系统统计数据
export const getSystemStats = async (req, res) => {
  try {
    // 获取各种统计数据
    const [userCount] = await pool.execute('SELECT COUNT(*) as count FROM users');
    const [collegeCount] = await pool.execute('SELECT COUNT(*) as count FROM colleges');
    const [majorCount] = await pool.execute('SELECT COUNT(*) as count FROM majors');
    const [buildingCount] = await pool.execute('SELECT COUNT(*) as count FROM dorm_buildings');
    const [repairCount] = await pool.execute('SELECT COUNT(*) as count FROM repair_requests');
    
    // 检查公告表是否存在
    let announcementCount = 0;
    try {
      const [annCount] = await pool.execute('SELECT COUNT(*) as count FROM announcements');
      announcementCount = annCount[0].count;
    } catch (error) {
      // 表不存在时忽略错误
    }

    // 获取待处理维修请求数量
    const [pendingRepairs] = await pool.execute(`
      SELECT COUNT(*) as count FROM repair_requests 
      WHERE status IN ('待处理', '已指派', '维修中')
    `);

    // 获取各角色用户数量
    const [roleStats] = await pool.execute(`
      SELECT ur.role_name, COUNT(u.id) as count
      FROM user_roles ur
      LEFT JOIN users u ON ur.id = u.role_id
      GROUP BY ur.id, ur.role_name
    `);

    res.json({
      success: true,
      data: {
        totalUsers: userCount[0].count,
        totalColleges: collegeCount[0].count,
        totalMajors: majorCount[0].count,
        totalBuildings: buildingCount[0].count,
        totalRepairs: repairCount[0].count,
        totalAnnouncements: announcementCount,
        pendingRepairs: pendingRepairs[0].count,
        roleStats: roleStats
      }
    });

  } catch (error) {
    console.error('获取统计数据错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};

// 获取最新公告
export const getLatestAnnouncements = async (req, res) => {
  try {
    const limit = req.query.limit || 3;
    
    let announcements = [];
    try {
      const [results] = await pool.execute(`
        SELECT a.*, u.name as author_name
        FROM announcements a
        LEFT JOIN users u ON a.author_id = u.id
        ORDER BY a.created_at DESC
        LIMIT ?
      `, [parseInt(limit)]);
      
      announcements = results.map(ann => ({
        id: ann.id,
        title: ann.title,
        content: ann.content,
        authorId: ann.author_id,
        authorName: ann.author_name,
        createdAt: ann.created_at,
        scope: ann.scope,
        targetId: ann.target_id
      }));
    } catch (error) {
      // 公告表不存在时返回空数组
    }

    res.json({
      success: true,
      data: announcements
    });

  } catch (error) {
    console.error('获取最新公告错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};

// 获取最新维修请求
export const getLatestRepairs = async (req, res) => {
  try {
    const limit = req.query.limit || 3;
    
    const [repairs] = await pool.execute(`
      SELECT rr.*, u.name as student_name, db.name as building_name
      FROM repair_requests rr
      LEFT JOIN users u ON rr.student_id = u.id
      LEFT JOIN dorm_buildings db ON rr.dorm_building_id = db.id
      WHERE rr.status IN ('待处理', '已指派', '维修中')
      ORDER BY rr.created_at DESC
      LIMIT ?
    `, [parseInt(limit)]);

    const formattedRepairs = repairs.map(repair => ({
      id: repair.id,
      description: repair.description,
      status: repair.status,
      studentName: repair.student_name,
      buildingName: repair.building_name,
      roomNumber: repair.room_number,
      createdAt: repair.created_at
    }));

    res.json({
      success: true,
      data: formattedRepairs
    });

  } catch (error) {
    console.error('获取最新维修请求错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};
