import express from 'express';
import { getRepairs, createRepair, updateRepairStatus, deleteRepair } from '../controllers/repairController.js';
import { authenticateToken, requireRole } from '../middleware/auth.js';

const router = express.Router();

// 所有维修请求路由都需要认证
router.use(authenticateToken);

// GET /api/repairs - 获取维修请求列表
router.get('/', getRepairs);

// POST /api/repairs - 创建维修请求（学生、宿舍管理员）
router.post('/', requireRole(['学生', '宿舍管理员']), createRepair);

// PUT /api/repairs/:id/status - 更新维修请求状态
router.put('/:id/status', updateRepairStatus);

// DELETE /api/repairs/:id - 删除维修请求
router.delete('/:id', deleteRepair);

export default router;
