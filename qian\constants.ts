import { User, UserRole, College, Major, DormBuilding, Room, RoomType, RepairRequest, RepairStatus, Announcement, UtilityBill, Bed, BedStatus, Violation, LateReturn, Visitor, CivilizedDormScore } from './types';

export const APP_NAME = "易宿管";

export const MOCK_ROOM_IMAGE_URL = "https://picsum.photos/seed/room/600/400"; // Generic room image

export const MOCK_USERS: User[] = [
  { id: 'sysadmin01', name: '系统管理员用户', email: '<EMAIL>', password: 'password123', role: UserRole.SYSTEM_ADMIN },
  { id: 'dormadmin01', name: '张三 (A栋宿舍管理员)', email: '<EMAIL>', password: 'password123', role: UserRole.DORM_ADMIN, dormBuilding: 'A栋 (阿尔法楼)' },
  { id: 'dormadmin02', name: '李四 (B栋宿舍管理员)', email: '<EMAIL>', password: 'password123', role: UserRole.DORM_ADMIN, dormBuilding: 'B栋 (贝塔公寓)' },
  { id: 'student01', name: '王五', email: '<EMAIL>', password: 'password123', phone: '13000000001', role: UserRole.STUDENT, college: '工程学院', major: '计算机科学', dormBuilding: 'A栋 (阿尔法楼)', roomNumber: '101', emergencyContactName: '王大锤', emergencyContactPhone: '13800138000' },
  { id: 'student02', name: '赵六', email: '<EMAIL>', password: 'password123', phone: '13000000002', role: UserRole.STUDENT, college: '文理学院', major: '历史学', dormBuilding: 'B栋 (贝塔公寓)', roomNumber: '205', emergencyContactName: '赵铁柱', emergencyContactPhone: '13900139000' },
  { id: 'student03', name: '孙七', email: '<EMAIL>', password: 'password123', phone: '13000000003', role: UserRole.STUDENT, college: '工程学院', major: '机械工程', dormBuilding: 'A栋 (阿尔法楼)', roomNumber: '102', emergencyContactName: '孙悟空', emergencyContactPhone: '13700137000' },
  { id: 'repair01', name: '维修工丁师傅', email: '<EMAIL>', password: 'password123', role: UserRole.REPAIR_STAFF },
];

export const MOCK_COLLEGES: College[] = [
  { id: 'col01', name: '工程学院' },
  { id: 'col02', name: '文理学院' },
  { id: 'col03', name: '商学院' },
];

export const MOCK_MAJORS: Major[] = [
  { id: 'maj01', name: '计算机科学', collegeId: 'col01' },
  { id: 'maj02', name: '机械工程', collegeId: 'col01' },
  { id: 'maj03', name: '历史学', collegeId: 'col02' },
  { id: 'maj04', name: '文学', collegeId: 'col02' },
  { id: 'maj05', name: '金融学', collegeId: 'col03' },
];

export const MOCK_DORM_BUILDINGS: DormBuilding[] = [
  { id: 'bldgA', name: 'A栋 (阿尔法楼)', floors: 5, totalRooms: 50, assignedAdminId: 'dormadmin01' },
  { id: 'bldgB', name: 'B栋 (贝塔公寓)', floors: 4, totalRooms: 40, assignedAdminId: 'dormadmin02' },
  { id: 'bldgC', name: 'C栋 (伽马学舍)', floors: 6, totalRooms: 60 },
];

export const MOCK_ROOMS: Room[] = [
  { id: 'roomA101', roomNumber: '101', dormBuildingId: 'bldgA', floor: 1, type: RoomType.HEXA, capacity: 6, occupiedBeds: 1 },
  { id: 'roomA102', roomNumber: '102', dormBuildingId: 'bldgA', floor: 1, type: RoomType.DOUBLE, capacity: 2, occupiedBeds: 1 },
  { id: 'roomB205', roomNumber: '205', dormBuildingId: 'bldgB', floor: 2, type: RoomType.HEXA, capacity: 6, occupiedBeds: 1 },
  { id: 'roomC301', roomNumber: '301', dormBuildingId: 'bldgC', floor: 3, type: RoomType.SINGLE, capacity: 1, occupiedBeds: 0 },
];

export const MOCK_BEDS: Bed[] = [
  // Room A101 (Hexa, student01 in bed 1)
  { id: 'bedA101-1', roomId: 'roomA101', bedNumber: '1', status: BedStatus.OCCUPIED, studentId: 'student01' },
  { id: 'bedA101-2', roomId: 'roomA101', bedNumber: '2', status: BedStatus.VACANT },
  { id: 'bedA101-3', roomId: 'roomA101', bedNumber: '3', status: BedStatus.VACANT },
  { id: 'bedA101-4', roomId: 'roomA101', bedNumber: '4', status: BedStatus.VACANT },
  { id: 'bedA101-5', roomId: 'roomA101', bedNumber: '5', status: BedStatus.VACANT },
  { id: 'bedA101-6', roomId: 'roomA101', bedNumber: '6', status: BedStatus.VACANT },
  
  // Room A102 (Double, student03 in bed 1)
  { id: 'bedA102-1', roomId: 'roomA102', bedNumber: '1', status: BedStatus.OCCUPIED, studentId: 'student03'},
  { id: 'bedA102-2', roomId: 'roomA102', bedNumber: '2', status: BedStatus.VACANT },

  // Room B205 (Hexa, student02 in bed 1)
  { id: 'bedB205-1', roomId: 'roomB205', bedNumber: '1', status: BedStatus.OCCUPIED, studentId: 'student02' },
  { id: 'bedB205-2', roomId: 'roomB205', bedNumber: '2', status: BedStatus.VACANT },
  { id: 'bedB205-3', roomId: 'roomB205', bedNumber: '3', status: BedStatus.VACANT },
  { id: 'bedB205-4', roomId: 'roomB205', bedNumber: '4', status: BedStatus.VACANT },
  { id: 'bedB205-5', roomId: 'roomB205', bedNumber: '5', status: BedStatus.VACANT },
  { id: 'bedB205-6', roomId: 'roomB205', bedNumber: '6', status: BedStatus.VACANT },
  
  // Room C301 (Single, vacant) - No beds explicitly listed as it's 1 capacity and 0 occupied, bed could be dynamically inferred or added if needed for specific logic.
  // For consistency, let's add one vacant bed for C301 if it's managed.
  { id: 'bedC301-1', roomId: 'roomC301', bedNumber: '1', status: BedStatus.VACANT },
];


export const MOCK_REPAIR_REQUESTS: RepairRequest[] = [
  { 
    id: 'repair001', 
    studentId: 'student01', 
    studentName: '王五',
    roomNumber: '101', 
    dormBuilding: 'A栋 (阿尔法楼)', 
    description: '卫生间水龙头漏水严重，无法关闭。', 
    contact: '<EMAIL>', 
    status: RepairStatus.ASSIGNED, 
    submittedAt: '2024-07-20T10:00:00Z',
    imageUrl: 'https://picsum.photos/seed/tap/300/200',
    assignedTo: 'repair01',
    assignedToName: '维修工丁师傅',
    updates: [
        { timestamp: '2024-07-20T11:00:00Z', updatedBy: '张三 (A栋宿舍管理员)', notes: '已指派给丁师傅处理。', newStatus: RepairStatus.ASSIGNED }
    ]
  },
  { 
    id: 'repair002', 
    studentId: 'student02',
    studentName: '赵六', 
    roomNumber: '205', 
    dormBuilding: 'B栋 (贝塔公寓)', 
    description: '书桌台灯不亮，更换灯泡无效。', 
    contact: '<EMAIL>', 
    status: RepairStatus.PENDING, 
    submittedAt: '2024-07-21T14:30:00Z',
    imageUrl: 'https://picsum.photos/seed/lamp/300/200',
    updates: []
  },
  { 
    id: 'repair003', 
    studentId: 'student03',
    studentName: '孙七', 
    roomNumber: '102', 
    dormBuilding: 'A栋 (阿尔法楼)', 
    description: '空调制冷效果差，噪音大。', 
    contact: '<EMAIL>', 
    status: RepairStatus.IN_PROGRESS, 
    submittedAt: '2024-07-22T08:15:00Z', 
    assignedTo: 'repair01',
    assignedToName: '维修工丁师傅',
    updates: [
        { timestamp: '2024-07-22T09:00:00Z', updatedBy: '张三 (A栋宿舍管理员)', notes: '指派给丁师傅。', newStatus: RepairStatus.ASSIGNED },
        { timestamp: '2024-07-22T14:00:00Z', updatedBy: '维修工丁师傅', notes: '已上门查看，初步判断为压缩机问题，明日带零件更换。', newStatus: RepairStatus.IN_PROGRESS }
    ]
  },
   { 
    id: 'repair004', 
    studentId: 'student01',
    studentName: '王五', 
    roomNumber: '101', 
    dormBuilding: 'A栋 (阿尔法楼)', 
    description: '窗户卡住，无法完全关闭。', 
    contact: '<EMAIL>', 
    status: RepairStatus.COMPLETED, 
    submittedAt: '2024-07-19T16:00:00Z', 
    assignedTo: 'repair01',
    assignedToName: '维修工丁师傅',
    updates: [
        { timestamp: '2024-07-19T17:00:00Z', updatedBy: '张三 (A栋宿舍管理员)', notes: '指派给丁师傅。', newStatus: RepairStatus.ASSIGNED },
        { timestamp: '2024-07-20T09:30:00Z', updatedBy: '维修工丁师傅', notes: '已处理，更换了卡扣。', newStatus: RepairStatus.COMPLETED }
    ]
  }
];

export const MOCK_ANNOUNCEMENTS: Announcement[] = [
  { id: 'anno001', title: '近期维修安排通知', content: '请注意，A栋将于7月25日上午9点至下午5点进行计划内维修。', authorId: 'sysadmin01', authorName: '系统管理员', createdAt: '2024-07-19T08:00:00Z', scope: 'All' },
  { id: 'anno002', title: '宿舍会议 - B栋', content: 'B栋全体住宿学生务必参加7月26日晚上7点在公共活动室举行的会议。', authorId: 'dormadmin02', authorName: '李四', createdAt: '2024-07-20T11:00:00Z', scope: 'DormBuilding', targetId: 'bldgB' },
];

export const MOCK_UTILITY_BILLS: UtilityBill[] = [
  { id: 'bill001', studentId: 'student01', roomId: 'roomA101', month: '2024-06', electricityUsage: 50, electricityCost: 25, waterUsage: 5, waterCost: 10, totalCost: 35, isPaid: true },
  { id: 'bill002', studentId: 'student02', roomId: 'roomB205', month: '2024-06', electricityUsage: 60, electricityCost: 30, waterUsage: 6, waterCost: 12, totalCost: 42, isPaid: false },
  { id: 'bill003', studentId: 'student01', roomId: 'roomA101', month: '2024-05', electricityUsage: 45, electricityCost: 22.5, waterUsage: 4, waterCost: 8, totalCost: 30.5, isPaid: false },
  { id: 'bill004', studentId: 'student03', roomId: 'roomA102', month: '2024-06', electricityUsage: 30, electricityCost: 15, waterUsage: 3, waterCost: 6, totalCost: 21, isPaid: true },
];

export const MOCK_VIOLATIONS: Violation[] = [
  { id: 'vio001', studentId: 'student01', studentName: '王五', dormBuildingId: 'bldgA', date: '2024-07-15', type: '违规电器', description: '宿舍内使用大功率吹风机', actionTaken: '口头警告', recordedBy: 'dormadmin01' },
  { id: 'vio002', studentId: 'student02', studentName: '赵六', dormBuildingId: 'bldgB', date: '2024-07-16', type: '晚归', description: '超过规定时间返校', actionTaken: '记录一次', recordedBy: 'dormadmin02' },
];

export const MOCK_LATE_RETURNS: LateReturn[] = [
  { id: 'lr001', studentId: 'student01', studentName: '王五', dormBuildingId: 'bldgA', date: '2024-07-18', time: '23:30', reason: '图书馆学习', recordedBy: 'dormadmin01' },
  { id: 'lr002', studentId: 'student03', studentName: '孙七', dormBuildingId: 'bldgA', date: '2024-07-19', time: '00:15', reason: '社团活动', recordedBy: 'dormadmin01' },
];

export const MOCK_VISITORS: Visitor[] = [
  { id: 'vis001', visitorName: '访客甲', visitorIdNumber: '123456789012345X', reason: '探亲', entryTime: '2024-07-20T14:00:00Z', exitTime: '2024-07-20T16:00:00Z', visitedStudentId: 'student01', visitedStudentName: '王五', dormBuildingId: 'bldgA', recordedBy: 'dormadmin01' },
  { id: 'vis002', visitorName: '访客乙', visitorIdNumber: '987654321098765Y', reason: '送东西', entryTime: '2024-07-21T10:00:00Z', visitedStudentId: 'student02', visitedStudentName: '赵六', dormBuildingId: 'bldgB', recordedBy: 'dormadmin02' },
];

export const MOCK_CIVILIZED_DORM_SCORES: CivilizedDormScore[] = [
  { id: 'cds001', dormBuildingId: 'bldgA', roomId: 'roomA101', date: '2024-07-01', score: 95, notes: '卫生良好，物品摆放整齐。', recordedBy: 'dormadmin01' },
  { id: 'cds002', dormBuildingId: 'bldgB', roomId: 'roomB205', date: '2024-07-01', score: 88, notes: '阳台有杂物，已提醒。', recordedBy: 'dormadmin02' },
  { id: 'cds003', dormBuildingId: 'bldgA', roomId: 'roomA102', date: '2024-07-01', score: 92, notes: '整体不错。', recordedBy: 'dormadmin01' },
];

// Helper to find building ID by name, as currentUser has building name
export const getBuildingIdByName = (name?: string): string | undefined => {
  if (!name) return undefined;
  return MOCK_DORM_BUILDINGS.find(b => b.name === name)?.id;
};