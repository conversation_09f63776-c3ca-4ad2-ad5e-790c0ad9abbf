import fetch from 'node-fetch';

const API_BASE = 'http://localhost:3002/api';

async function addTestAssignments() {
  try {
    console.log('🔐 登录获取token...');
    
    // 1. 登录获取token
    const loginResponse = await fetch(`${API_BASE}/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password123'
      })
    });
    
    const loginData = await loginResponse.json();
    if (!loginData.success) {
      throw new Error('登录失败');
    }
    
    const token = loginData.token;
    console.log('✅ 登录成功');
    
    // 2. 获取当前学生列表
    console.log('\n👥 获取学生列表...');
    const studentsResponse = await fetch(`${API_BASE}/student-allocation/students?building_id=bldgA`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    const studentsData = await studentsResponse.json();
    if (studentsData.success) {
      console.log(`当前学生数量: ${studentsData.data.length}`);
      studentsData.data.forEach(s => {
        console.log(`  - ${s.name} (${s.id}) - 房间: ${s.room_number || '未分配'}`);
      });
    }
    
    // 3. 获取可用床位
    console.log('\n🛏️ 获取可用床位...');
    const bedsResponse = await fetch(`${API_BASE}/student-allocation/available-beds?building_id=bldgA`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    const bedsData = await bedsResponse.json();
    if (bedsData.success) {
      console.log(`可用床位数量: ${bedsData.data.length}`);
      bedsData.data.slice(0, 5).forEach(b => {
        console.log(`  - 房间${b.room_number} 床位${b.bed_number} (${b.bed_id})`);
      });
    }
    
    // 4. 执行一些分配操作
    console.log('\n🎯 执行床位分配...');
    
    const unassignedStudents = studentsData.data.filter(s => !s.room_number);
    const availableBeds = bedsData.data;
    
    const assignmentCount = Math.min(unassignedStudents.length, availableBeds.length, 3);
    
    for (let i = 0; i < assignmentCount; i++) {
      const student = unassignedStudents[i];
      const bed = availableBeds[i];
      
      try {
        const assignResponse = await fetch(`${API_BASE}/student-allocation/assign`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            student_id: student.id,
            bed_id: bed.bed_id
          })
        });
        
        const assignData = await assignResponse.json();
        if (assignData.success) {
          console.log(`  ✅ ${student.name} -> 房间${bed.room_number} 床位${bed.bed_number}`);
        } else {
          console.log(`  ❌ 分配失败: ${student.name} - ${assignData.message}`);
        }
      } catch (error) {
        console.log(`  ❌ 分配错误: ${student.name} - ${error.message}`);
      }
      
      // 稍微延迟避免请求过快
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    // 5. 显示最终结果
    console.log('\n📊 最终结果:');
    const finalStudentsResponse = await fetch(`${API_BASE}/student-allocation/students?building_id=bldgA`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    
    const finalStudentsData = await finalStudentsResponse.json();
    if (finalStudentsData.success) {
      const assigned = finalStudentsData.data.filter(s => s.room_number);
      const unassigned = finalStudentsData.data.filter(s => !s.room_number);
      
      console.log(`已分配学生: ${assigned.length}人`);
      assigned.forEach(s => {
        console.log(`  - ${s.name} -> 房间${s.room_number}`);
      });
      
      console.log(`未分配学生: ${unassigned.length}人`);
      unassigned.forEach(s => {
        console.log(`  - ${s.name}`);
      });
    }
    
    console.log('\n🎉 测试数据添加完成！');
    
  } catch (error) {
    console.error('❌ 操作失败:', error.message);
  }
}

addTestAssignments();
