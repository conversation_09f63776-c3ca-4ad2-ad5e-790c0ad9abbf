<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>易宿管 - 智能宿舍管理系统</title>
  
  <!-- 预加载字体 -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  
  <!-- Tailwind CSS -->
  <script src="https://cdn.tailwindcss.com"></script>
  
  <!-- Font Awesome -->
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
  
  <!-- Tailwind Config -->
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: {
              light: '#818cf8',
              DEFAULT: '#4f46e5',
              dark: '#4338ca',
            },
            secondary: {
              light: '#fbbf24',
              DEFAULT: '#f59e0b',
              dark: '#d97706',
            },
            neutral: { 
              light: '#e0f2fe',
              DEFAULT: '#cbd5e1',
              dark: '#334155',
            }
          },
          fontFamily: {
            'sans': ['Inter', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'sans-serif'],
          },
          animation: {
            'fade-in': 'fadeIn 0.6s ease-out',
            'slide-in': 'slideIn 0.5s ease-out',
            'pulse-slow': 'pulse 2s infinite',
            'shimmer': 'shimmer 1.5s infinite',
          },
          keyframes: {
            fadeIn: {
              '0%': { opacity: '0', transform: 'translateY(20px)' },
              '100%': { opacity: '1', transform: 'translateY(0)' },
            },
            slideIn: {
              '0%': { opacity: '0', transform: 'translateX(-20px)' },
              '100%': { opacity: '1', transform: 'translateX(0)' },
            },
            shimmer: {
              '0%': { backgroundPosition: '-200px 0' },
              '100%': { backgroundPosition: 'calc(200px + 100%) 0' },
            },
          },
        }
      }
    }
  </script>
  
  <!-- Import Map -->
  <script type="importmap">
  {
    "imports": {
      "react/": "https://esm.sh/react@^19.1.0/",
      "react": "https://esm.sh/react@^19.1.0",
      "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
      "react-router-dom": "https://esm.sh/react-router-dom@^7.6.2"
    }
  }
  </script>
  
  <!-- Custom CSS -->
  <link rel="stylesheet" href="/index.css">
  
  <!-- Favicon -->
  <link rel="icon" type="image/svg+xml" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🏢</text></svg>">
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 font-sans antialiased">
  <div id="root"></div>
  <script type="module" src="/index.tsx"></script>
</body>
</html>