import { pool } from '../config/database.js';

// 获取房间列表（支持按宿舍楼筛选）
export const getRooms = async (req, res) => {
  try {
    const { building_id } = req.query;
    
    let query = `
      SELECT r.*, db.name as building_name
      FROM rooms r
      LEFT JOIN dorm_buildings db ON r.dorm_building_id = db.id
    `;
    let params = [];

    if (building_id) {
      query += ' WHERE r.dorm_building_id = ?';
      params.push(building_id);
    }

    query += ' ORDER BY r.dorm_building_id, r.floor, r.room_number';

    const [rooms] = await pool.execute(query, params);

    res.json({
      success: true,
      data: rooms
    });

  } catch (error) {
    console.error('获取房间列表错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};

// 创建房间
export const createRoom = async (req, res) => {
  try {
    const { 
      room_number, 
      dorm_building_id, 
      floor, 
      type, 
      capacity 
    } = req.body;

    if (!room_number || !dorm_building_id || !floor || !type || !capacity) {
      return res.status(400).json({
        success: false,
        message: '房间号、宿舍楼、楼层、类型和容量都是必填项'
      });
    }

    // 检查房间号是否已存在
    const [existingRooms] = await pool.execute(
      'SELECT id FROM rooms WHERE room_number = ? AND dorm_building_id = ?',
      [room_number, dorm_building_id]
    );

    if (existingRooms.length > 0) {
      return res.status(400).json({
        success: false,
        message: '该房间号在此宿舍楼中已存在'
      });
    }

    // 生成房间ID
    const roomId = `room_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // 插入新房间
    await pool.execute(
      `INSERT INTO rooms (
        id, room_number, dorm_building_id, floor, type, capacity, occupied_beds
      ) VALUES (?, ?, ?, ?, ?, ?, ?)`,
      [
        roomId, room_number, dorm_building_id, floor, type, capacity, 0
      ]
    );

    // 为房间创建床位
    for (let i = 1; i <= capacity; i++) {
      await pool.execute(
        `INSERT INTO beds (id, room_id, bed_number, status) VALUES (?, ?, ?, ?)`,
        [`bed_${roomId}_${i}`, roomId, i.toString(), '空闲']
      );
    }

    res.status(201).json({
      success: true,
      message: '房间创建成功',
      data: {
        roomId
      }
    });

  } catch (error) {
    console.error('创建房间错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};

// 获取房间详情和床位信息
export const getRoomDetails = async (req, res) => {
  try {
    const { id } = req.params;

    // 获取房间信息
    const [rooms] = await pool.execute(`
      SELECT r.*, db.name as building_name
      FROM rooms r
      LEFT JOIN dorm_buildings db ON r.dorm_building_id = db.id
      WHERE r.id = ?
    `, [id]);

    if (rooms.length === 0) {
      return res.status(404).json({
        success: false,
        message: '房间不存在'
      });
    }

    // 获取床位信息
    const [beds] = await pool.execute(`
      SELECT b.*, u.name as student_name
      FROM beds b
      LEFT JOIN users u ON b.student_id = u.id
      WHERE b.room_id = ?
      ORDER BY b.bed_number
    `, [id]);

    res.json({
      success: true,
      data: {
        room: rooms[0],
        beds: beds
      }
    });

  } catch (error) {
    console.error('获取房间详情错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};

// 更新房间信息
export const updateRoom = async (req, res) => {
  try {
    const { id } = req.params;
    const { 
      room_number, 
      floor, 
      type, 
      capacity 
    } = req.body;

    // 检查房间是否存在
    const [existingRooms] = await pool.execute('SELECT * FROM rooms WHERE id = ?', [id]);
    if (existingRooms.length === 0) {
      return res.status(404).json({
        success: false,
        message: '房间不存在'
      });
    }

    const currentRoom = existingRooms[0];

    // 如果容量发生变化，需要调整床位
    if (capacity !== currentRoom.capacity) {
      if (capacity < currentRoom.occupied_beds) {
        return res.status(400).json({
          success: false,
          message: '新容量不能小于当前已入住人数'
        });
      }

      // 删除现有床位
      await pool.execute('DELETE FROM beds WHERE room_id = ?', [id]);

      // 创建新床位
      for (let i = 1; i <= capacity; i++) {
        await pool.execute(
          `INSERT INTO beds (id, room_id, bed_number, status) VALUES (?, ?, ?, ?)`,
          [`bed_${id}_${i}_${Date.now()}`, id, i.toString(), '空闲']
        );
      }
    }

    // 更新房间信息
    await pool.execute(
      `UPDATE rooms SET 
        room_number = ?, floor = ?, type = ?, capacity = ?
       WHERE id = ?`,
      [room_number, floor, type, capacity, id]
    );

    res.json({
      success: true,
      message: '房间更新成功'
    });

  } catch (error) {
    console.error('更新房间错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};

// 删除房间
export const deleteRoom = async (req, res) => {
  try {
    const { id } = req.params;

    // 检查房间是否存在
    const [existingRooms] = await pool.execute('SELECT occupied_beds FROM rooms WHERE id = ?', [id]);
    if (existingRooms.length === 0) {
      return res.status(404).json({
        success: false,
        message: '房间不存在'
      });
    }

    // 检查是否有学生入住
    if (existingRooms[0].occupied_beds > 0) {
      return res.status(400).json({
        success: false,
        message: '无法删除房间，还有学生入住'
      });
    }

    // 删除房间（床位会因为外键约束自动删除）
    await pool.execute('DELETE FROM rooms WHERE id = ?', [id]);

    res.json({
      success: true,
      message: '房间删除成功'
    });

  } catch (error) {
    console.error('删除房间错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};
