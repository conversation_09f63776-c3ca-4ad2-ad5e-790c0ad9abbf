import { pool } from '../config/database.js';

// 获取维修请求列表
export const getRepairs = async (req, res) => {
  try {
    const user = req.user;
    let query = `
      SELECT rr.*, u.name as studentName, db.name as dormBuilding,
             staff.name as assignedStaffName
      FROM repair_requests rr
      LEFT JOIN users u ON rr.student_id = u.id
      LEFT JOIN dorm_buildings db ON rr.dorm_building_id = db.id
      LEFT JOIN users staff ON rr.assigned_staff_id = staff.id
    `;
    let params = [];

    // 根据用户角色过滤数据
    if (user.role === '学生') {
      query += ' WHERE rr.student_id = ?';
      params.push(user.id);
    } else if (user.role === '宿舍管理员') {
      // 宿舍管理员只能看到自己管理的楼栋的维修请求
      const [buildingRows] = await pool.execute(
        'SELECT id FROM dorm_buildings WHERE assigned_admin_id = ?',
        [user.id]
      );
      
      if (buildingRows.length > 0) {
        const buildingIds = buildingRows.map(row => row.id);
        query += ` WHERE rr.dorm_building_id IN (${buildingIds.map(() => '?').join(',')})`;
        params.push(...buildingIds);
      } else {
        // 如果没有分配楼栋，返回空结果
        query += ' WHERE 1 = 0';
      }
    } else if (user.role === '维修人员') {
      // 维修人员可以看到所有维修请求，但主要关注分配给自己的
      query += ' WHERE (rr.assigned_staff_id = ? OR rr.assigned_staff_id IS NULL)';
      params.push(user.id);
    }
    // 系统管理员可以看到所有维修请求，不需要额外过滤

    query += ' ORDER BY rr.created_at DESC';

    const [rows] = await pool.execute(query, params);

    res.json({
      success: true,
      data: {
        repairRequests: rows
      }
    });

  } catch (error) {
    console.error('获取维修请求列表错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};

// 创建维修请求
export const createRepair = async (req, res) => {
  try {
    const { description, roomNumber, dormBuilding } = req.body;
    const studentId = req.user.id;

    if (!description) {
      return res.status(400).json({
        success: false,
        message: '问题描述不能为空'
      });
    }

    // 查找宿舍楼ID
    let dormBuildingId = null;
    if (dormBuilding) {
      const [buildingRows] = await pool.execute(
        'SELECT id FROM dorm_buildings WHERE name = ?',
        [dormBuilding]
      );
      dormBuildingId = buildingRows.length > 0 ? buildingRows[0].id : null;
    }

    // 查找房间ID
    let roomId = null;
    if (roomNumber && dormBuildingId) {
      const [roomRows] = await pool.execute(
        'SELECT id FROM rooms WHERE room_number = ? AND dorm_building_id = ?',
        [roomNumber, dormBuildingId]
      );
      roomId = roomRows.length > 0 ? roomRows[0].id : null;
    }

    // 生成维修请求ID
    const repairId = `repair_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // 插入维修请求
    await pool.execute(
      `INSERT INTO repair_requests (
        id, student_id, room_id, room_number, dorm_building_id, description, status
      ) VALUES (?, ?, ?, ?, ?, ?, ?)`,
      [repairId, studentId, roomId, roomNumber, dormBuildingId, description, '待处理']
    );

    res.status(201).json({
      success: true,
      message: '维修请求提交成功',
      data: {
        repairId
      }
    });

  } catch (error) {
    console.error('创建维修请求错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};

// 更新维修请求状态
export const updateRepairStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { status, notes, assignedStaffId } = req.body;
    const user = req.user;

    if (!status) {
      return res.status(400).json({
        success: false,
        message: '状态不能为空'
      });
    }

    // 检查维修请求是否存在
    const [existingRepairs] = await pool.execute(
      'SELECT * FROM repair_requests WHERE id = ?',
      [id]
    );

    if (existingRepairs.length === 0) {
      return res.status(404).json({
        success: false,
        message: '维修请求不存在'
      });
    }

    const repair = existingRepairs[0];

    // 权限检查
    if (user.role === '学生' && repair.student_id !== user.id) {
      return res.status(403).json({
        success: false,
        message: '只能操作自己的维修请求'
      });
    }

    // 更新维修请求
    let updateQuery = 'UPDATE repair_requests SET status = ?';
    let updateParams = [status];

    if (notes) {
      updateQuery += ', notes = ?';
      updateParams.push(notes);
    }

    if (assignedStaffId) {
      updateQuery += ', assigned_staff_id = ?';
      updateParams.push(assignedStaffId);
    }

    updateQuery += ' WHERE id = ?';
    updateParams.push(id);

    await pool.execute(updateQuery, updateParams);

    res.json({
      success: true,
      message: '维修请求状态更新成功'
    });

  } catch (error) {
    console.error('更新维修请求状态错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};

// 删除维修请求
export const deleteRepair = async (req, res) => {
  try {
    const { id } = req.params;
    const user = req.user;

    // 检查维修请求是否存在
    const [existingRepairs] = await pool.execute(
      'SELECT student_id FROM repair_requests WHERE id = ?',
      [id]
    );

    if (existingRepairs.length === 0) {
      return res.status(404).json({
        success: false,
        message: '维修请求不存在'
      });
    }

    const repair = existingRepairs[0];

    // 权限检查：只有学生本人或系统管理员可以删除
    if (user.role === '学生' && repair.student_id !== user.id) {
      return res.status(403).json({
        success: false,
        message: '只能删除自己的维修请求'
      });
    } else if (user.role !== '学生' && user.role !== '系统管理员') {
      return res.status(403).json({
        success: false,
        message: '权限不足'
      });
    }

    // 删除维修请求
    await pool.execute('DELETE FROM repair_requests WHERE id = ?', [id]);

    res.json({
      success: true,
      message: '维修请求删除成功'
    });

  } catch (error) {
    console.error('删除维修请求错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};
