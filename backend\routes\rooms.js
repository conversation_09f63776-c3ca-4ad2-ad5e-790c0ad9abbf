import express from 'express';
import { 
  getRooms, 
  createRoom, 
  getRoomDetails, 
  updateRoom, 
  deleteRoom 
} from '../controllers/roomController.js';
import { authenticateToken } from '../middleware/auth.js';

const router = express.Router();

// 获取房间列表
router.get('/', authenticateToken, getRooms);

// 获取房间详情
router.get('/:id', authenticateToken, getRoomDetails);

// 创建房间
router.post('/', authenticateToken, createRoom);

// 更新房间
router.put('/:id', authenticateToken, updateRoom);

// 删除房间
router.delete('/:id', authenticateToken, deleteRoom);

export default router;
