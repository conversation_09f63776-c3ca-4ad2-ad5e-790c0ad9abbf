import mysql from 'mysql2/promise';
import bcrypt from 'bcrypt';
import dotenv from 'dotenv';

dotenv.config();

const insertTestData = async () => {
  let connection;
  
  try {
    console.log('🔄 开始插入测试数据...');
    
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || 'root',
      database: process.env.DB_NAME || 'dorm_management',
      charset: 'utf8mb4'
    });

    console.log('✅ 成功连接到数据库');

    // 生成密码哈希
    const passwordHash = await bcrypt.hash('password123', 10);
    console.log('✅ 密码哈希生成成功');

    // 1. 插入角色数据
    console.log('🔄 插入角色数据...');
    await connection.execute(`
      INSERT INTO user_roles (id, role_name, role_description) VALUES
      (1, '系统管理员', '系统管理员，拥有所有权限'),
      (2, '宿舍管理员', '宿舍管理员，管理特定宿舍楼'),
      (3, '学生', '学生用户'),
      (4, '维修人员', '维修人员，处理维修请求')
      ON DUPLICATE KEY UPDATE role_name = VALUES(role_name)
    `);
    console.log('✅ 角色数据插入成功');

    // 2. 插入学院数据
    console.log('🔄 插入学院数据...');
    await connection.execute(`
      INSERT INTO colleges (id, name) VALUES
      ('college01', '计算机科学学院'),
      ('college02', '电子工程学院'),
      ('college03', '机械工程学院'),
      ('college04', '经济管理学院')
      ON DUPLICATE KEY UPDATE name = VALUES(name)
    `);
    console.log('✅ 学院数据插入成功');

    // 3. 插入专业数据
    console.log('🔄 插入专业数据...');
    await connection.execute(`
      INSERT INTO majors (id, name, college_id) VALUES
      ('major01', '计算机科学与技术', 'college01'),
      ('major02', '软件工程', 'college01'),
      ('major03', '电子信息工程', 'college02'),
      ('major04', '通信工程', 'college02'),
      ('major05', '机械设计制造及其自动化', 'college03'),
      ('major06', '工商管理', 'college04'),
      ('major07', '会计学', 'college04')
      ON DUPLICATE KEY UPDATE name = VALUES(name)
    `);
    console.log('✅ 专业数据插入成功');

    // 4. 插入宿舍楼数据
    console.log('🔄 插入宿舍楼数据...');
    await connection.execute(`
      INSERT INTO dorm_buildings (id, name, floors, total_rooms) VALUES
      ('bldgA', 'A栋', 6, 120),
      ('bldgB', 'B栋', 8, 160),
      ('bldgC', 'C栋', 5, 100)
      ON DUPLICATE KEY UPDATE name = VALUES(name)
    `);
    console.log('✅ 宿舍楼数据插入成功');

    // 5. 插入用户数据
    console.log('🔄 插入用户数据...');
    await connection.execute(`
      INSERT INTO users (id, name, email, password, role_id, phone, college_id, major_id, dorm_building_id, emergency_contact_name, emergency_contact_phone) VALUES
      (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?),
      (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?),
      (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?),
      (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?),
      (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?),
      (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?),
      (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      ON DUPLICATE KEY UPDATE name = VALUES(name)
    `, [
      'sysadmin01', '系统管理员', '<EMAIL>', passwordHash, 1, '13800000001', null, null, null, null, null,
      'repair01', '爱德华·修理工', '<EMAIL>', passwordHash, 4, '13800000005', null, null, null, null, null,
      'dormadmin01', '张三', '<EMAIL>', passwordHash, 2, '13800000002', null, null, 'bldgA', null, null,
      'dormadmin02', '李四', '<EMAIL>', passwordHash, 2, '13800000003', null, null, 'bldgB', null, null,
      'student01', '王五', '<EMAIL>', passwordHash, 3, '13800000004', 'college01', 'major01', 'bldgA', '王父', '13900000001',
      'student02', '赵六', '<EMAIL>', passwordHash, 3, '13800000006', 'college02', 'major03', 'bldgB', '赵母', '13900000002',
      'student03', '孙七', '<EMAIL>', passwordHash, 3, '13800000007', 'college01', 'major02', 'bldgA', '孙父', '13900000003'
    ]);
    console.log('✅ 用户数据插入成功');

    // 6. 更新宿舍楼的管理员分配
    console.log('🔄 分配宿舍管理员...');
    await connection.execute('UPDATE dorm_buildings SET assigned_admin_id = ? WHERE id = ?', ['dormadmin01', 'bldgA']);
    await connection.execute('UPDATE dorm_buildings SET assigned_admin_id = ? WHERE id = ?', ['dormadmin02', 'bldgB']);
    console.log('✅ 宿舍管理员分配成功');

    // 7. 插入维修请求数据
    console.log('🔄 插入维修请求数据...');
    await connection.execute(`
      INSERT INTO repair_requests (id, student_id, room_number, dorm_building_id, description, status, assigned_staff_id, notes) VALUES
      ('repair001', 'student01', '101', 'bldgA', '水龙头漏水', '待处理', null, null),
      ('repair002', 'student02', '205', 'bldgB', '灯泡坏了', '已指派', 'repair01', '已安排维修人员'),
      ('repair003', 'student03', '102', 'bldgA', '门锁损坏', '已完成', 'repair01', '已更换新门锁')
      ON DUPLICATE KEY UPDATE description = VALUES(description)
    `);
    console.log('✅ 维修请求数据插入成功');

    // 验证数据
    console.log('\n🔄 验证插入的数据...');
    
    const [userCount] = await connection.execute('SELECT COUNT(*) as count FROM users');
    const [collegeCount] = await connection.execute('SELECT COUNT(*) as count FROM colleges');
    const [majorCount] = await connection.execute('SELECT COUNT(*) as count FROM majors');
    const [repairCount] = await connection.execute('SELECT COUNT(*) as count FROM repair_requests');
    const [roleCount] = await connection.execute('SELECT COUNT(*) as count FROM user_roles');

    console.log('\n📊 数据统计:');
    console.log(`   ✅ 用户数量: ${userCount[0].count}`);
    console.log(`   ✅ 学院数量: ${collegeCount[0].count}`);
    console.log(`   ✅ 专业数量: ${majorCount[0].count}`);
    console.log(`   ✅ 维修请求数量: ${repairCount[0].count}`);
    console.log(`   ✅ 角色数量: ${roleCount[0].count}`);

    // 显示测试账户信息
    console.log('\n🔑 测试账户信息:');
    console.log('   📧 系统管理员: <EMAIL> / password123');
    console.log('   📧 宿舍管理员: <EMAIL> / password123');
    console.log('   📧 学生: <EMAIL> / password123');
    console.log('   📧 维修人员: <EMAIL> / password123');

    console.log('\n🎉 测试数据插入完成！现在可以使用前端系统了！');
    
  } catch (error) {
    console.error('❌ 数据插入失败:', error.message);
    console.error('错误详情:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
};

insertTestData();
