import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

dotenv.config();

const checkTables = async () => {
  let connection;
  
  try {
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || 'root',
      database: process.env.DB_NAME || 'dorm_management',
      charset: 'utf8mb4'
    });

    console.log('✅ 连接到数据库成功');

    // 检查所有表
    const [tables] = await connection.execute('SHOW TABLES');
    console.log('📋 现有表:');
    tables.forEach(table => {
      console.log(`   - ${Object.values(table)[0]}`);
    });

    // 检查用户表结构
    if (tables.some(table => Object.values(table)[0] === 'users')) {
      console.log('\n📋 用户表结构:');
      const [columns] = await connection.execute('DESCRIBE users');
      columns.forEach(col => {
        console.log(`   - ${col.Field}: ${col.Type} ${col.Null === 'NO' ? 'NOT NULL' : 'NULL'} ${col.Key ? `(${col.Key})` : ''}`);
      });
    }
    
  } catch (error) {
    console.error('❌ 检查失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
};

checkTables();
