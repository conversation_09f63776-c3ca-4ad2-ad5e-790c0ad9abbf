export enum UserRole {
  SYSTEM_ADMIN = '系统管理员',
  DORM_ADMIN = '宿舍管理员',
  STUDENT = '学生',
  REPAIR_STAFF = '维修人员',
}

export interface User {
  id: string;
  name: string;
  email: string;
  password?: string; // Added password field
  phone?: string; // Student's own phone number
  role: UserRole;
  college?: string;
  major?: string;
  dormBuilding?: string; // This is dorm building NAME
  roomNumber?: string;
  emergencyContactName?: string;
  emergencyContactPhone?: string;
}

export interface College {
  id: string;
  name: string;
}

export interface Major {
  id: string;
  name: string;
  collegeId: string;
}

export interface DormBuilding {
  id: string;
  name: string;
  floors: number;
  totalRooms: number;
  assignedAdminId?: string;
}

export enum RoomType {
  SINGLE = '单人间',
  DOUBLE = '双人间',
  QUAD = '四人间',
  HEXA = '六人间', // Added six-person room
}

export interface Room {
  id:string;
  roomNumber: string;
  dormBuildingId: string;
  floor: number;
  type: RoomType;
  capacity: number;
  occupiedBeds: number;
}

export enum BedStatus {
  VACANT = '空闲',
  OCCUPIED = '已入住',
}

export interface Bed {
  id: string;
  roomId: string;
  bedNumber: string;
  status: BedStatus;
  studentId?: string;
}


export enum RepairStatus {
  PENDING = '待处理',
  ASSIGNED = '已指派',
  IN_PROGRESS = '维修中',
  COMPLETED = '已完成',
  CONFIRMED = '已确认',
  CANCELLED = '已取消',
}

export interface RepairRequest {
  id: string;
  studentId: string;
  studentName: string;
  roomNumber: string;
  dormBuilding: string; // This is dorm building NAME
  description: string;
  imageUrl?: string;
  contact: string;
  status: RepairStatus;
  submittedAt: string;
  assignedTo?: string; // RepairStaff ID
  assignedToName?: string;
  updates?: RepairUpdate[];
}

export interface RepairUpdate {
  timestamp: string;
  updatedBy: string; // User ID or name
  notes: string;
  newStatus?: RepairStatus;
}

export interface Announcement {
  id: string;
  title: string;
  content: string;
  authorId: string;
  authorName: string;
  createdAt: string;
  scope: 'All' | 'College' | 'DormBuilding';
  targetId?: string; // College ID or DormBuilding ID if not 'All'
}

export interface UtilityBill {
  id: string;
  studentId: string;
  roomId: string;
  month: string; // YYYY-MM
  electricityUsage: number; // kWh
  electricityCost: number; // currency
  waterUsage: number; // m^3
  waterCost: number; // currency
  totalCost: number;
  isPaid: boolean;
}

export interface LateReturn {
  id: string;
  studentId: string;
  studentName: string;
  dormBuildingId: string; // Added for filtering
  date: string;
  time: string;
  reason: string;
  recordedBy: string; // DormAdmin ID
}

export interface Visitor {
  id: string;
  visitorName: string;
  visitorIdNumber: string;
  reason: string;
  entryTime: string;
  exitTime?: string;
  visitedStudentId: string;
  visitedStudentName: string;
  dormBuildingId: string; // Added for filtering
  recordedBy: string; // DormAdmin ID
}

export interface Violation {
  id: string;
  studentId: string;
  studentName: string;
  dormBuildingId: string; // Added for filtering
  date: string;
  type: string; // e.g., 'Prohibited Item', 'Noise Complaint'
  description: string;
  actionTaken?: string;
  recordedBy: string; // DormAdmin ID
}

export interface CivilizedDormScore {
  id: string;
  dormBuildingId: string; // Used for filtering
  roomId: string;
  date: string;
  score: number;
  notes?: string;
  recordedBy: string; // DormAdmin ID
}