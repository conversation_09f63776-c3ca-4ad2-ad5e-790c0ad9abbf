
import React, { useState, useEffect, FormEvent } from 'react';
import { User, Room, Bed, BedStatus, UserRole } from '../../types';
import { useAuth } from '../../contexts/AuthContext';
import Table from '../../components/Table';
import Card from '../../components/Card';
import Button from '../../components/Button';
import Modal from '../../components/Modal';
import Input from '../../components/Input';

const API_BASE_URL = 'http://localhost:3002/api';

const StudentAllocationPage: React.FC = () => {
  const { currentUser } = useAuth();
  const [students, setStudents] = useState<any[]>([]);
  const [vacantBeds, setVacantBeds] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [adminBuildingId, setAdminBuildingId] = useState<string>('');

  const [isAssignModalOpen, setIsAssignModalOpen] = useState(false);
  const [isEditInfoModalOpen, setIsEditInfoModalOpen] = useState(false);
  const [selectedStudent, setSelectedStudent] = useState<any | null>(null);
  const [studentToEdit, setStudentToEdit] = useState<any>({});
  const [selectedBedId, setSelectedBedId] = useState<string>('');

  // 获取宿舍楼信息
  const fetchBuildingInfo = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_BASE_URL}/dorm-buildings`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          const userBuilding = data.data.find((building: any) =>
            building.assigned_admin_id === currentUser?.id
          );
          if (userBuilding) {
            setAdminBuildingId(userBuilding.id);
          } else {
            setIsLoading(false);
          }
        }
      }
    } catch (error) {
      console.error('获取宿舍楼信息错误:', error);
      setIsLoading(false);
    }
  };

  // 获取学生列表
  const fetchStudents = async () => {
    if (!adminBuildingId) return;

    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_BASE_URL}/student-allocation/students?building_id=${adminBuildingId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setStudents(data.data);
        }
      }
    } catch (error) {
      console.error('获取学生列表错误:', error);
    }
  };

  // 获取空闲床位
  const fetchVacantBeds = async () => {
    if (!adminBuildingId) return;

    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_BASE_URL}/student-allocation/vacant-beds?building_id=${adminBuildingId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setVacantBeds(data.data);
        }
      }
    } catch (error) {
      console.error('获取空闲床位错误:', error);
    }
  };

  useEffect(() => {
    if (currentUser) {
      fetchBuildingInfo();
    }
  }, [currentUser]);

  useEffect(() => {
    if (adminBuildingId) {
      Promise.all([fetchStudents(), fetchVacantBeds()]).then(() => {
        setIsLoading(false);
      });
    }
  }, [adminBuildingId]);

  if (!currentUser || currentUser.role !== UserRole.DORM_ADMIN) {
    return <p>权限不足。</p>;
  }

  if (isLoading) {
    return (
      <Card title="学生分配管理">
        <div className="flex justify-center items-center py-8">
          <div className="text-gray-500">加载中...</div>
        </div>
      </Card>
    );
  }

  if (!adminBuildingId) {
    return (
      <Card title="学生分配管理">
        <div className="p-8 text-center">
          <div className="text-red-500 mb-4">
            <i className="fas fa-exclamation-triangle text-4xl mb-3"></i>
            <h3 className="text-lg font-semibold">未分配宿舍楼</h3>
          </div>
          <p className="text-gray-600">您还没有被分配管理任何宿舍楼，请联系系统管理员进行分配。</p>
        </div>
      </Card>
    );
  }

  const buildingName = currentUser.dormBuilding || "未知楼栋";

  const studentColumns = [
    { header: '姓名', accessor: 'name' as keyof any },
    { header: '邮箱', accessor: 'email' as keyof any },
    { header: '学生电话', accessor: (student: any) => student.phone || '未填写' },
    { header: '当前房间', accessor: (student: any) => student.room_number || '未分配' },
    { header: '当前床位', accessor: (student: any) => student.bed_number || '未分配' },
    {
      header: '第一紧急联系人信息',
      accessor: (student: any) => {
        if (student.emergency_contact_name && student.emergency_contact_phone) {
          return `${student.emergency_contact_name} (${student.emergency_contact_phone})`;
        }
        if (student.emergency_contact_name) {
          return student.emergency_contact_name;
        }
        if (student.emergency_contact_phone) {
          return student.emergency_contact_phone;
        }
        return '未填写';
      }
    },
    {
      header: '操作',
      accessor: 'id' as keyof any,
      render: (student: any) => (
        <div className="space-x-2 whitespace-nowrap">
            <Button size="sm" variant="ghost" onClick={() => handleOpenAssignModal(student)}>
                {student.room_number ? '调整床位' : '分配床位'}
            </Button>
            <Button size="sm" variant="ghost" onClick={() => handleOpenEditInfoModal(student)}>
                <i className="fas fa-user-edit mr-1"></i> 编辑信息
            </Button>
            {student.room_number && (
                <Button size="sm" variant="danger" onClick={() => handleUnassignStudent(student.id)}>
                    取消分配
                </Button>
            )}
        </div>
      ),
    },
  ];
  
  const handleOpenAssignModal = (student: any) => {
    setSelectedStudent(student);
    setSelectedBedId('');
    setIsAssignModalOpen(true);
  };

  const handleCloseAssignModal = () => {
    setIsAssignModalOpen(false);
    setSelectedStudent(null);
    setSelectedBedId('');
  };

  const handleOpenEditInfoModal = (student: any) => {
    setSelectedStudent(student);
    setStudentToEdit({ ...student });
    setIsEditInfoModalOpen(true);
  };

  const handleCloseEditInfoModal = () => {
    setIsEditInfoModalOpen(false);
    setSelectedStudent(null);
    setStudentToEdit({});
  };
  
  const handleStudentInfoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setStudentToEdit(prev => ({ ...prev, [name]: value }));
  };

  const handleSaveStudentInfo = async (e: FormEvent) => {
    e.preventDefault();
    if (selectedStudent) {
      try {
        const token = localStorage.getItem('token');
        const response = await fetch(`${API_BASE_URL}/student-allocation/emergency-contact/${selectedStudent.id}`, {
          method: 'PUT',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            emergency_contact_name: studentToEdit.emergency_contact_name,
            emergency_contact_phone: studentToEdit.emergency_contact_phone,
          }),
        });

        if (response.ok) {
          const data = await response.json();
          if (data.success) {
            // 重新获取学生列表
            await fetchStudents();
            handleCloseEditInfoModal();
            alert('学生信息更新成功！');
          } else {
            alert(data.message || '更新失败');
          }
        } else {
          alert('更新失败，请重试');
        }
      } catch (error) {
        console.error('更新学生信息错误:', error);
        alert('更新失败，请重试');
      }
    }
  };


  const handleAssignStudent = async () => {
    if (selectedStudent && selectedBedId) {
      try {
        const token = localStorage.getItem('token');
        const response = await fetch(`${API_BASE_URL}/student-allocation/assign`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            student_id: selectedStudent.id,
            bed_id: selectedBedId,
          }),
        });

        if (response.ok) {
          const data = await response.json();
          if (data.success) {
            // 重新获取数据
            await Promise.all([fetchStudents(), fetchVacantBeds()]);
            handleCloseAssignModal();
            alert('学生床位分配成功！');
          } else {
            alert(data.message || '分配失败');
          }
        } else {
          const errorData = await response.json();
          alert(errorData.message || '分配失败');
        }
      } catch (error) {
        console.error('分配学生床位错误:', error);
        alert('分配失败，请重试');
      }
    }
  };
  
  const handleUnassignStudent = async (studentId: string) => {
    if (!window.confirm("确定要取消该学生的床位分配吗？")) return;

    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_BASE_URL}/student-allocation/unassign/${studentId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          // 重新获取数据
          await Promise.all([fetchStudents(), fetchVacantBeds()]);
          alert('学生床位分配已取消！');
        } else {
          alert(data.message || '取消分配失败');
        }
      } else {
        const errorData = await response.json();
        alert(errorData.message || '取消分配失败');
      }
    } catch (error) {
      console.error('取消学生床位分配错误:', error);
      alert('取消分配失败，请重试');
    }
  };


  return (
    <div className="space-y-6">
      <Card title={`${buildingName} - 学生分配管理`}>
        <h3 className="text-xl font-semibold mb-2">学生列表</h3>
        <Table columns={studentColumns} data={students} keyExtractor={student => student.id} emptyStateMessage="此楼栋暂无学生记录。"/>
      </Card>

      <Card title={`${buildingName} - 可用床位`}>
          {vacantBeds.length === 0 ? <p>暂无可用床位。</p> : (
            <ul>
                {vacantBeds.map(bed => (
                    <li key={bed.id} className="py-1">房间 {bed.room_number} ({bed.floor}楼) - 床位 {bed.bed_number}</li>
                ))}
            </ul>
          )}
      </Card>

      {/* Assign/Adjust Bed Modal */}
      {selectedStudent && (
        <Modal isOpen={isAssignModalOpen} onClose={handleCloseAssignModal} title={`为 ${selectedStudent.name} 分配/调整床位`}>
          <div className="space-y-4">
            <p>选择一个空闲床位进行分配：</p>
            <select
              value={selectedBedId}
              onChange={(e) => setSelectedBedId(e.target.value)}
              className="block w-full px-3 py-2 border border-neutral-DEFAULT rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
            >
              <option value="">-- 请选择床位 --</option>
              {vacantBeds.map(bed => (
                <option key={bed.id} value={bed.id}>房间 {bed.room_number} ({bed.floor}楼) - 床位 {bed.bed_number}</option>
              ))}
              {vacantBeds.length === 0 && <option disabled>此楼栋暂无可分配床位</option>}
            </select>
            <div className="flex justify-end space-x-2 pt-4">
              <Button variant="ghost" onClick={handleCloseAssignModal}>取消</Button>
              <Button onClick={handleAssignStudent} disabled={!selectedBedId}>确认分配</Button>
            </div>
          </div>
        </Modal>
      )}

      {/* Edit Student Info Modal */}
      {selectedStudent && isEditInfoModalOpen && (
        <Modal isOpen={isEditInfoModalOpen} onClose={handleCloseEditInfoModal} title={`编辑 ${selectedStudent.name} 的信息`}>
            <form onSubmit={handleSaveStudentInfo} className="space-y-4">
                <Input label="姓名" value={studentToEdit.name || ''} readOnly disabled containerClassName="bg-gray-100 p-2 rounded"/>
                <Input label="邮箱" value={studentToEdit.email || ''} readOnly disabled containerClassName="bg-gray-100 p-2 rounded"/>
                <Input label="学生电话" value={studentToEdit.phone || '未填写'} readOnly disabled containerClassName="bg-gray-100 p-2 rounded"/>
                <Input
                    name="emergency_contact_name"
                    label="第一紧急联系人姓名"
                    value={studentToEdit.emergency_contact_name || ''}
                    onChange={handleStudentInfoChange}
                />
                <Input
                    name="emergency_contact_phone"
                    label="第一紧急联系人电话"
                    type="tel"
                    value={studentToEdit.emergency_contact_phone || ''}
                    onChange={handleStudentInfoChange}
                    placeholder="例如: 13800138000"
                />
                 <div className="flex justify-end space-x-2 pt-4">
                    <Button type="button" variant="ghost" onClick={handleCloseEditInfoModal}>取消</Button>
                    <Button type="submit">保存更改</Button>
                </div>
            </form>
        </Modal>
      )}
    </div>
  );
};

export default StudentAllocationPage;