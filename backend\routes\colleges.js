import express from 'express';
import { getColleges, createCollege, updateCollege, deleteCollege } from '../controllers/collegeController.js';
import { authenticateToken, requireRole } from '../middleware/auth.js';

const router = express.Router();

// 所有学院路由都需要认证
router.use(authenticateToken);

// GET /api/colleges - 获取学院列表
router.get('/', getColleges);

// POST /api/colleges - 创建学院（仅系统管理员）
router.post('/', requireRole(['系统管理员']), createCollege);

// PUT /api/colleges/:id - 更新学院（仅系统管理员）
router.put('/:id', requireRole(['系统管理员']), updateCollege);

// DELETE /api/colleges/:id - 删除学院（仅系统管理员）
router.delete('/:id', requireRole(['系统管理员']), deleteCollege);

export default router;
