import mysql from 'mysql2/promise';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const quickSetup = async () => {
  let connection;
  
  try {
    console.log('🔄 快速设置数据库...');
    
    // 连接到MySQL服务器（不指定数据库）
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || 'root',
      charset: 'utf8mb4',
      multipleStatements: true
    });

    console.log('✅ 成功连接到MySQL服务器');

    // 读取SQL文件
    const sqlPath = path.join(__dirname, '../quick-setup.sql');
    const sqlContent = fs.readFileSync(sqlPath, 'utf8');

    // 执行SQL
    await connection.execute(sqlContent);
    console.log('✅ SQL执行成功');

    // 验证数据
    await connection.execute('USE dorm_management');
    
    const [userCount] = await connection.execute('SELECT COUNT(*) as count FROM users');
    const [collegeCount] = await connection.execute('SELECT COUNT(*) as count FROM colleges');
    const [roleCount] = await connection.execute('SELECT COUNT(*) as count FROM user_roles');

    console.log('\n📊 数据统计:');
    console.log(`   ✅ 用户数量: ${userCount[0].count}`);
    console.log(`   ✅ 学院数量: ${collegeCount[0].count}`);
    console.log(`   ✅ 角色数量: ${roleCount[0].count}`);

    console.log('\n🎉 数据库快速设置完成！');
    console.log('\n🔑 测试账户 (密码: password123):');
    console.log('   📧 系统管理员: <EMAIL>');
    console.log('   📧 宿舍管理员: <EMAIL>');
    console.log('   📧 学生: <EMAIL>');
    console.log('   📧 维修人员: <EMAIL>');
    
  } catch (error) {
    console.error('❌ 快速设置失败:', error.message);
    console.error('错误详情:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
};

quickSetup();
