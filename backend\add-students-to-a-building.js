import { pool } from './config/database.js';
import bcrypt from 'bcrypt';

async function addStudentsToABuilding() {
  try {
    console.log('👥 给A栋添加4个学生：孙七、周八、吴九、郑十...');
    
    // 生成密码哈希
    const passwordHash = await bcrypt.hash('password123', 10);
    
    // 要添加的4个学生
    const students = [
      { id: 'student03', name: '孙七', email: '<EMAIL>' },
      { id: 'student04', name: '周八', email: '<EMAIL>' },
      { id: 'student05', name: '吴九', email: '<EMAIL>' },
      { id: 'student06', name: '郑十', email: '<EMAIL>' }
    ];
    
    console.log('\n📝 添加学生用户...');
    for (const student of students) {
      try {
        await pool.execute(`
          INSERT INTO users (id, name, email, password, role_id, phone, college_id, major_id, dorm_building_id, emergency_contact_name, emergency_contact_phone) 
          VALUES (?, ?, ?, ?, 3, ?, 'college01', 'major01', 'bldgA', ?, ?)
        `, [
          student.id, 
          student.name, 
          student.email, 
          passwordHash,
          '138' + String(Math.floor(Math.random() * 100000000)).padStart(8, '0'), // 随机手机号
          student.name.slice(0, 1) + '父', // 紧急联系人
          '139' + String(Math.floor(Math.random() * 100000000)).padStart(8, '0') // 紧急联系人电话
        ]);
        console.log(`✅ 添加学生: ${student.name} -> A栋`);
      } catch (error) {
        if (error.code === 'ER_DUP_ENTRY') {
          console.log(`⚠️ 学生已存在: ${student.name}，更新宿舍楼信息...`);
          // 如果学生已存在，更新其宿舍楼信息
          await pool.execute(`
            UPDATE users SET dorm_building_id = 'bldgA' WHERE id = ?
          `, [student.id]);
          console.log(`✅ 更新学生宿舍楼: ${student.name} -> A栋`);
        } else {
          console.log(`❌ 添加失败: ${student.name} - ${error.message}`);
        }
      }
    }
    
    // 检查A栋的学生情况
    console.log('\n📊 检查A栋学生情况:');
    const [buildingAStudents] = await pool.execute(`
      SELECT u.id, u.name, u.dorm_building_id, u.room_number
      FROM users u
      WHERE u.role_id = 3 AND u.dorm_building_id = 'bldgA'
      ORDER BY u.name
    `);
    
    console.log(`A栋学生总数: ${buildingAStudents.length}`);
    buildingAStudents.forEach(s => {
      console.log(`  - ${s.name} (${s.id}) - 房间: ${s.room_number || '未分配'}`);
    });
    
    // 检查A栋的空闲床位
    console.log('\n🛏️ 检查A栋空闲床位:');
    const [availableBeds] = await pool.execute(`
      SELECT b.id, b.bed_number, r.room_number, r.floor
      FROM beds b
      JOIN rooms r ON b.room_id = r.id
      WHERE r.dorm_building_id = 'bldgA' AND b.status = '空闲'
      ORDER BY r.floor, r.room_number, b.bed_number
      LIMIT 10
    `);
    
    console.log(`A栋空闲床位: ${availableBeds.length}个`);
    availableBeds.forEach(b => {
      console.log(`  - ${b.floor}楼 房间${b.room_number} 床位${b.bed_number} (${b.id})`);
    });
    
    // 为未分配房间的学生自动分配床位
    console.log('\n🎯 自动分配床位...');
    const unassignedStudents = buildingAStudents.filter(s => !s.room_number);
    const assignmentCount = Math.min(unassignedStudents.length, availableBeds.length);
    
    for (let i = 0; i < assignmentCount; i++) {
      const student = unassignedStudents[i];
      const bed = availableBeds[i];
      
      try {
        // 分配床位
        await pool.execute(`
          UPDATE beds SET status = '已入住', student_id = ? WHERE id = ?
        `, [student.id, bed.id]);
        
        // 更新学生房间信息
        await pool.execute(`
          UPDATE users SET room_number = ? WHERE id = ?
        `, [bed.room_number, student.id]);
        
        // 更新房间已住人数
        await pool.execute(`
          UPDATE rooms SET occupied_beds = (
            SELECT COUNT(*) FROM beds WHERE room_id = (
              SELECT room_id FROM beds WHERE id = ?
            ) AND status = '已入住'
          ) WHERE id = (
            SELECT room_id FROM beds WHERE id = ?
          )
        `, [bed.id, bed.id]);
        
        console.log(`✅ ${student.name} -> ${bed.floor}楼 房间${bed.room_number} 床位${bed.bed_number}`);
      } catch (error) {
        console.log(`❌ 分配失败: ${student.name} - ${error.message}`);
      }
    }
    
    // 显示最终结果
    console.log('\n🎉 最终结果:');
    const [finalStudents] = await pool.execute(`
      SELECT u.id, u.name, u.room_number, b.bed_number
      FROM users u
      LEFT JOIN beds b ON b.student_id = u.id
      WHERE u.role_id = 3 AND u.dorm_building_id = 'bldgA'
      ORDER BY u.room_number, b.bed_number
    `);
    
    const assigned = finalStudents.filter(s => s.room_number);
    const unassigned = finalStudents.filter(s => !s.room_number);
    
    console.log(`A栋学生总数: ${finalStudents.length}`);
    console.log(`已分配床位: ${assigned.length}人`);
    assigned.forEach(s => {
      console.log(`  ✅ ${s.name} -> 房间${s.room_number} 床位${s.bed_number}`);
    });
    
    console.log(`未分配床位: ${unassigned.length}人`);
    unassigned.forEach(s => {
      console.log(`  ⏳ ${s.name} -> 待分配`);
    });
    
    await pool.end();
    console.log('\n🎊 A栋学生添加完成！张三现在应该能看到5个学生了！');
  } catch (error) {
    console.error('❌ 操作失败:', error);
    process.exit(1);
  }
}

addStudentsToABuilding();
