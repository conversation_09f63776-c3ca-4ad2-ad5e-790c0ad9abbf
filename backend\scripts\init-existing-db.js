import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

dotenv.config();

const initExistingDB = async () => {
  let connection;
  
  try {
    console.log('🔄 开始初始化现有数据库...');
    
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || 'root',
      database: process.env.DB_NAME || 'dorm_management',
      charset: 'utf8mb4'
    });

    console.log('✅ 成功连接到数据库');

    // 首先清理现有数据（如果需要）
    console.log('🔄 清理现有数据...');
    await connection.execute('DELETE FROM users');
    await connection.execute('DELETE FROM colleges');
    await connection.execute('DELETE FROM majors');
    await connection.execute('DELETE FROM dorm_buildings');
    await connection.execute('DELETE FROM repair_requests');

    // 插入或更新角色数据
    console.log('🔄 设置用户角色...');
    await connection.execute(`
      INSERT INTO user_roles (id, role_name, role_description) VALUES
      (1, '系统管理员', '系统管理员，拥有所有权限'),
      (2, '宿舍管理员', '宿舍管理员，管理特定宿舍楼'),
      (3, '学生', '学生用户'),
      (4, '维修人员', '维修人员，处理维修请求')
      ON DUPLICATE KEY UPDATE 
      role_name = VALUES(role_name),
      role_description = VALUES(role_description)
    `);

    // 插入学院数据
    console.log('🔄 插入学院数据...');
    await connection.execute(`
      INSERT IGNORE INTO colleges (id, name) VALUES
      ('college01', '计算机科学学院'),
      ('college02', '电子工程学院'),
      ('college03', '机械工程学院'),
      ('college04', '经济管理学院')
    `);

    // 插入专业数据
    console.log('🔄 插入专业数据...');
    await connection.execute(`
      INSERT IGNORE INTO majors (id, name, college_id) VALUES
      ('major01', '计算机科学与技术', 'college01'),
      ('major02', '软件工程', 'college01'),
      ('major03', '电子信息工程', 'college02'),
      ('major04', '通信工程', 'college02'),
      ('major05', '机械设计制造及其自动化', 'college03'),
      ('major06', '工商管理', 'college04'),
      ('major07', '会计学', 'college04')
    `);

    // 插入宿舍楼数据
    console.log('🔄 插入宿舍楼数据...');
    await connection.execute(`
      INSERT IGNORE INTO dorm_buildings (id, name, floors, total_rooms) VALUES
      ('bldgA', 'A栋', 6, 120),
      ('bldgB', 'B栋', 8, 160),
      ('bldgC', 'C栋', 5, 100)
    `);

    // 插入用户数据（使用role_id）
    console.log('🔄 插入用户数据...');
    await connection.execute(`
      INSERT IGNORE INTO users (id, name, email, password, role_id, phone, college_id, major_id, dorm_building_id, emergency_contact_name, emergency_contact_phone) VALUES
      ('sysadmin01', '系统管理员', '<EMAIL>', '$2b$10$rOvHPGkwYvYFQVlXiXHxHOuBJKxj8qMxqGjZvQjZvQjZvQjZvQjZvO', 1, '13800000001', NULL, NULL, NULL, NULL, NULL),
      ('repair01', '爱德华·修理工', '<EMAIL>', '$2b$10$rOvHPGkwYvYFQVlXiXHxHOuBJKxj8qMxqGjZvQjZvQjZvQjZvQjZvO', 4, '13800000005', NULL, NULL, NULL, NULL, NULL),
      ('dormadmin01', '张三', '<EMAIL>', '$2b$10$rOvHPGkwYvYFQVlXiXHxHOuBJKxj8qMxqGjZvQjZvQjZvQjZvQjZvO', 2, '13800000002', NULL, NULL, 'bldgA', NULL, NULL),
      ('dormadmin02', '李四', '<EMAIL>', '$2b$10$rOvHPGkwYvYFQVlXiXHxHOuBJKxj8qMxqGjZvQjZvQjZvQjZvQjZvO', 2, '13800000003', NULL, NULL, 'bldgB', NULL, NULL),
      ('student01', '王五', '<EMAIL>', '$2b$10$rOvHPGkwYvYFQVlXiXHxHOuBJKxj8qMxqGjZvQjZvQjZvQjZvQjZvO', 3, '13800000004', 'college01', 'major01', 'bldgA', '王父', '13900000001'),
      ('student02', '赵六', '<EMAIL>', '$2b$10$rOvHPGkwYvYFQVlXiXHxHOuBJKxj8qMxqGjZvQjZvQjZvQjZvQjZvO', 3, '13800000006', 'college02', 'major03', 'bldgB', '赵母', '13900000002'),
      ('student03', '孙七', '<EMAIL>', '$2b$10$rOvHPGkwYvYFQVlXiXHxHOuBJKxj8qMxqGjZvQjZvQjZvQjZvQjZvO', 3, '13800000007', 'college01', 'major02', 'bldgA', '孙父', '13900000003')
    `);

    // 更新宿舍楼的管理员分配
    console.log('🔄 分配宿舍管理员...');
    await connection.execute('UPDATE dorm_buildings SET assigned_admin_id = ? WHERE id = ?', ['dormadmin01', 'bldgA']);
    await connection.execute('UPDATE dorm_buildings SET assigned_admin_id = ? WHERE id = ?', ['dormadmin02', 'bldgB']);

    // 插入维修请求数据
    console.log('🔄 插入维修请求数据...');
    await connection.execute(`
      INSERT IGNORE INTO repair_requests (id, student_id, room_number, dorm_building_id, description, status, assigned_staff_id, notes) VALUES
      ('repair001', 'student01', '101', 'bldgA', '水龙头漏水', '待处理', NULL, NULL),
      ('repair002', 'student02', '205', 'bldgB', '灯泡坏了', '已指派', 'repair01', '已安排维修人员'),
      ('repair003', 'student03', '102', 'bldgA', '门锁损坏', '已完成', 'repair01', '已更换新门锁')
    `);

    console.log('✅ 测试数据插入成功');

    // 验证数据
    console.log('🔄 验证数据...');
    const [userCount] = await connection.execute('SELECT COUNT(*) as count FROM users');
    const [collegeCount] = await connection.execute('SELECT COUNT(*) as count FROM colleges');
    const [repairCount] = await connection.execute('SELECT COUNT(*) as count FROM repair_requests');

    console.log(`✅ 用户数量: ${userCount[0].count}`);
    console.log(`✅ 学院数量: ${collegeCount[0].count}`);
    console.log(`✅ 维修请求数量: ${repairCount[0].count}`);

    console.log('🎉 数据库初始化完成！');
    
  } catch (error) {
    console.error('❌ 数据库初始化失败:', error.message);
    console.error('错误详情:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
};

initExistingDB();
