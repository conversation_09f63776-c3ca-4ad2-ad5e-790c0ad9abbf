import React, { ReactNode, useState } from 'react';
import { NavLink, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { UserRole } from '../types';
import { APP_NAME } from '../constants';
import Button from '../components/Button';

interface NavItem {
  path: string;
  name: string;
  icon: string;
  roles: UserRole[];
}

const navItems: NavItem[] = [
  { path: '/dashboard', name: '仪表盘', icon: 'fa-tachometer-alt', roles: [UserRole.SYSTEM_ADMIN, UserRole.DORM_ADMIN, UserRole.STUDENT, UserRole.REPAIR_STAFF] },
  // System Admin
  { path: '/admin/users', name: '用户管理', icon: 'fa-users-cog', roles: [UserRole.SYSTEM_ADMIN] },
  { path: '/admin/colleges', name: '学院管理', icon: 'fa-landmark', roles: [UserRole.SYSTEM_ADMIN] },
  { path: '/admin/majors', name: '专业管理', icon: 'fa-book-open', roles: [UserRole.SYSTEM_ADMIN] },
  { path: '/admin/dorm-buildings', name: '宿舍楼管理', icon: 'fa-building', roles: [UserRole.SYSTEM_ADMIN] },
  // Dorm Admin
  { path: '/dorm-admin/rooms', name: '房间管理', icon: 'fa-door-open', roles: [UserRole.DORM_ADMIN] },
  { path: '/dorm-admin/allocation', name: '学生分配', icon: 'fa-user-plus', roles: [UserRole.DORM_ADMIN] },
  { path: '/dorm-admin/violations', name: '违规记录', icon: 'fa-exclamation-triangle', roles: [UserRole.DORM_ADMIN] },
  { path: '/dorm-admin/late-returns', name: '晚归记录', icon: 'fa-user-clock', roles: [UserRole.DORM_ADMIN] },
  { path: '/dorm-admin/visitors', name: '访客登记', icon: 'fa-id-card', roles: [UserRole.DORM_ADMIN] },
  { path: '/dorm-admin/civilized-dorm', name: '文明宿舍', icon: 'fa-star', roles: [UserRole.DORM_ADMIN] },
  // Student
  { path: '/student/my-info', name: '我的信息', icon: 'fa-user-circle', roles: [UserRole.STUDENT] },
  { path: '/student/utility-bills', name: '水电费账单', icon: 'fa-file-invoice-dollar', roles: [UserRole.STUDENT] },
  // Repair Staff
  { path: '/repair/my-tasks', name: '我的任务', icon: 'fa-tools', roles: [UserRole.REPAIR_STAFF] },
  // Common for multiple roles
  { path: '/repairs', name: '报修申请', icon: 'fa-wrench', roles: [UserRole.STUDENT, UserRole.DORM_ADMIN, UserRole.REPAIR_STAFF] },
  { path: '/announcements', name: '通知公告', icon: 'fa-bullhorn', roles: [UserRole.SYSTEM_ADMIN, UserRole.DORM_ADMIN, UserRole.STUDENT, UserRole.REPAIR_STAFF] },
];

const MainLayout: React.FC<{ children: ReactNode }> = ({ children }) => {
  const { currentUser, logout } = useAuth();
  const navigate = useNavigate();
  const [sidebarOpen, setSidebarOpen] = useState(false);

  if (!currentUser) {
    navigate('/login');
    return null; 
  }

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  const filteredNavItems = navItems.filter(item => item.roles.includes(currentUser.role));

  return (
    <div className="flex h-screen bg-gradient-to-br from-gray-50 to-blue-50">
      {/* Sidebar */}
      <aside className={`fixed inset-y-0 left-0 z-30 w-72 bg-gradient-to-b from-blue-900 via-blue-800 to-blue-900 text-white transform ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'} transition-all duration-300 ease-in-out md:relative md:translate-x-0 md:flex md:flex-col shadow-strong`}>
        {/* Logo Area */}
        <div className="flex items-center justify-center h-20 border-b border-blue-700 bg-gradient-to-r from-blue-800 to-blue-900">
          <div className="flex items-center">
            <div className="w-10 h-10 bg-white bg-opacity-20 rounded-xl flex items-center justify-center mr-3 backdrop-blur-sm">
              <i className="fas fa-university fa-xl text-white"></i>
            </div>
            <span className="text-xl font-bold text-white">{APP_NAME}</span>
          </div>
        </div>
        
        {/* Navigation */}
        <nav className="flex-grow p-6 space-y-3">
          {filteredNavItems.map((item) => (
            <NavLink
              key={item.path}
              to={item.path}
              onClick={() => setSidebarOpen(false)}
              className={({ isActive }) =>
                `flex items-center px-4 py-3 rounded-xl transition-all duration-300 ease-in-out group hover:bg-white hover:bg-opacity-10 hover:transform hover:translate-x-2 ${
                  isActive 
                    ? 'bg-gradient-to-r from-blue-600 to-blue-500 text-white shadow-lg' 
                    : 'text-blue-100 hover:text-white'
                }`
              }
            >
              <div className={`w-8 h-8 rounded-lg flex items-center justify-center mr-3 transition-all duration-300 ${
                'isActive' ? 'bg-white bg-opacity-20' : 'bg-blue-700 group-hover:bg-blue-600'
              }`}>
                <i className={`fas ${item.icon} text-sm`}></i>
              </div>
              <span className="font-medium">{item.name}</span>
            </NavLink>
          ))}
        </nav>
        
        {/* User Info & Logout */}
        <div className="p-6 border-t border-blue-700 bg-gradient-to-r from-blue-800 to-blue-900">
          <div className="mb-4 p-3 bg-white bg-opacity-10 rounded-xl backdrop-blur-sm">
            <div className="flex items-center">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-400 to-blue-500 rounded-full flex items-center justify-center mr-3">
                <i className="fas fa-user text-white"></i>
              </div>
              <div>
                <p className="text-white font-medium text-sm">{currentUser.name}</p>
                <p className="text-blue-200 text-xs">{currentUser.role}</p>
              </div>
            </div>
          </div>
          <Button 
            variant="ghost" 
            onClick={handleLogout} 
            className="w-full bg-white bg-opacity-10 hover:bg-opacity-20 text-white border-white border-opacity-20"
          >
            <i className="fas fa-sign-out-alt mr-2"></i> 退出登录
          </Button>
        </div>
      </aside>

      {/* Content Area */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Header */}
        <header className="bg-white shadow-soft h-16 flex items-center justify-between px-6 border-b border-gray-100">
          <div className="flex items-center">
            <button 
              onClick={() => setSidebarOpen(!sidebarOpen)} 
              className="text-gray-600 hover:text-gray-800 md:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors duration-200"
            >
              <i className={`fas ${sidebarOpen ? 'fa-times' : 'fa-bars'} text-xl`}></i>
            </button>
            <div className="ml-4">
              <h1 className="text-lg font-bold text-gray-800">欢迎回来, {currentUser.name}</h1>
              <p className="text-sm text-gray-500">{currentUser.role}</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            {/* Notifications */}
            <button className="relative p-2 text-gray-600 hover:text-gray-800 rounded-lg hover:bg-gray-100 transition-colors duration-200">
              <i className="fas fa-bell text-xl"></i>
              <span className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></span>
            </button>
            
            {/* User Menu */}
            <div className="flex items-center space-x-3 p-2 rounded-xl hover:bg-gray-50 transition-colors duration-200 cursor-pointer">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                <i className="fas fa-user text-white text-sm"></i>
              </div>
              <span className="text-gray-700 font-medium hidden sm:block">{currentUser.name}</span>
            </div>
          </div>
        </header>

        {/* Main Content */}
        <main className="flex-1 overflow-x-hidden overflow-y-auto p-6">
          <div className="animate-fade-in">
            {children}
          </div>
        </main>
      </div>
      
      {/* Mobile Overlay */}
      {sidebarOpen && (
        <div 
          className="fixed inset-0 z-20 bg-black bg-opacity-50 md:hidden animate-fade-in" 
          onClick={() => setSidebarOpen(false)}
        ></div>
      )}
    </div>
  );
};

export default MainLayout;