import { pool } from './config/database.js';

async function addLateReturnData() {
  try {
    console.log('🌙 添加晚归记录测试数据...');
    
    // 检查晚归记录表是否存在
    const [tables] = await pool.execute("SHOW TABLES LIKE 'late_returns'");
    if (tables.length === 0) {
      console.log('⚠️ 晚归记录表不存在，正在创建...');
      
      // 创建晚归记录表
      await pool.execute(`
        CREATE TABLE late_returns (
          id VARCHAR(50) PRIMARY KEY,
          student_id VARCHAR(50) NOT NULL,
          dorm_building_id VARCHAR(50) NOT NULL,
          date DATE NOT NULL,
          time TIME NOT NULL,
          reason TEXT,
          recorded_by VARCHAR(50) NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
          FOREIGN KEY (dorm_building_id) REFERENCES dorm_buildings(id) ON DELETE CASCADE,
          FOREIGN KEY (recorded_by) REFERENCES users(id) ON DELETE CASCADE
        )
      `);
      console.log('✅ 晚归记录表创建成功');
    } else {
      console.log('✅ 晚归记录表已存在');
    }
    
    // 清空现有数据
    await pool.execute('DELETE FROM late_returns');
    console.log('🗑️ 清空现有晚归记录数据');
    
    // 插入测试晚归记录数据
    const lateReturns = [
      {
        id: 'late001',
        student_id: 'student01', // 王五
        dorm_building_id: 'bldgA',
        date: '2024-07-18',
        time: '23:30:00',
        reason: '图书馆学习到很晚',
        recorded_by: 'dormadmin01' // 张三
      },
      {
        id: 'late002',
        student_id: 'student03', // 孙七
        dorm_building_id: 'bldgA',
        date: '2024-07-19',
        time: '00:15:00',
        reason: '社团活动结束较晚',
        recorded_by: 'dormadmin01' // 张三
      },
      {
        id: 'late003',
        student_id: 'student04', // 周八
        dorm_building_id: 'bldgA',
        date: '2024-07-20',
        time: '23:45:00',
        reason: '实验室项目加班',
        recorded_by: 'dormadmin01' // 张三
      },
      {
        id: 'late004',
        student_id: 'student01', // 王五
        dorm_building_id: 'bldgA',
        date: '2024-07-21',
        time: '00:30:00',
        reason: '朋友聚会',
        recorded_by: 'dormadmin01' // 张三
      }
    ];
    
    for (const lateReturn of lateReturns) {
      try {
        await pool.execute(`
          INSERT INTO late_returns (id, student_id, dorm_building_id, date, time, reason, recorded_by)
          VALUES (?, ?, ?, ?, ?, ?, ?)
        `, [
          lateReturn.id,
          lateReturn.student_id,
          lateReturn.dorm_building_id,
          lateReturn.date,
          lateReturn.time,
          lateReturn.reason,
          lateReturn.recorded_by
        ]);
        console.log(`✅ 添加晚归记录: ${lateReturn.id}`);
      } catch (error) {
        console.log(`❌ 添加失败: ${lateReturn.id} - ${error.message}`);
      }
    }
    
    // 查看结果
    console.log('\n📊 查看A栋晚归记录:');
    const [results] = await pool.execute(`
      SELECT lr.*, u.name as student_name, u.room_number
      FROM late_returns lr
      LEFT JOIN users u ON lr.student_id = u.id
      WHERE lr.dorm_building_id = 'bldgA'
      ORDER BY lr.date DESC, lr.time DESC
    `);
    
    console.log(`A栋晚归记录总数: ${results.length}`);
    results.forEach(lr => {
      console.log(`  - ${lr.student_name} (${lr.student_id}) - ${lr.date} ${lr.time} - ${lr.reason}`);
    });
    
    await pool.end();
    console.log('\n🎉 晚归记录数据添加完成！');
  } catch (error) {
    console.error('❌ 操作失败:', error);
    process.exit(1);
  }
}

addLateReturnData();
