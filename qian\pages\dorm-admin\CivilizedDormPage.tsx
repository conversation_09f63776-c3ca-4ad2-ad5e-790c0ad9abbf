
import React, { useState, FormEvent } from 'react';
import { CivilizedDormScore, Room, UserRole } from '../../types';
import { MOCK_CIVILIZED_DORM_SCORES, MOCK_ROOMS, getBuildingIdByName } from '../../constants';
import { useAuth } from '../../contexts/AuthContext';
import Table from '../../components/Table';
import Card from '../../components/Card';
import Button from '../../components/Button';
import Modal from '../../components/Modal';
import Input from '../../components/Input';

const CivilizedDormPage: React.FC = () => {
  const { currentUser } = useAuth();
  const [scores, setScores] = useState<CivilizedDormScore[]>(MOCK_CIVILIZED_DORM_SCORES);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [newScore, setNewScore] = useState<Partial<CivilizedDormScore>>({});

  if (!currentUser || currentUser.role !== UserRole.DORM_ADMIN) return <p>权限不足。</p>;

  const adminBuildingId = getBuildingIdByName(currentUser.dormBuilding);
  const buildingName = currentUser.dormBuilding || "未知楼栋";
  
  const roomsInAdminBuilding = MOCK_ROOMS.filter(r => r.dormBuildingId === adminBuildingId);
  const filteredScores = scores.filter(s => s.dormBuildingId === adminBuildingId);

  const columns = [
    { header: '房间号', accessor: (s: CivilizedDormScore) => MOCK_ROOMS.find(r => r.id === s.roomId)?.roomNumber || s.roomId },
    { header: '日期', accessor: 'date' as keyof CivilizedDormScore },
    { header: '分数', accessor: 'score' as keyof CivilizedDormScore },
    { header: '备注', accessor: (s:CivilizedDormScore) => s.notes || "N/A" },
    { 
      header: '操作', 
      accessor: 'id' as keyof CivilizedDormScore,
      render: (score: CivilizedDormScore) => (
        <Button size="sm" variant="danger" onClick={() => handleDelete(score.id)}><i className="fas fa-trash"></i></Button>
      )
    }
  ];

  const handleOpenModal = () => {
    setNewScore({ recordedBy: currentUser.id, dormBuildingId: adminBuildingId, date: new Date().toISOString().split('T')[0], score: 90 });
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setNewScore({});
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setNewScore(prev => ({ ...prev, [name]: name === 'score' ? parseInt(value) : value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (newScore.roomId && newScore.date && newScore.score !== undefined) {
      if (newScore.score < 0 || newScore.score > 100) {
          alert("分数必须在0到100之间。");
          return;
      }
      setScores([...scores, { ...newScore, id: `cds_${Date.now()}` } as CivilizedDormScore]);
      handleCloseModal();
    } else {
        alert("请填写所有必填项（房间、日期、分数）。");
    }
  };
  
  const handleDelete = (id: string) => {
    if (window.confirm("确定删除此评分记录吗?")) {
        setScores(scores.filter(s => s.id !== id));
    }
  };

  return (
    <Card title={`${buildingName} - 文明宿舍评比`} actions={<Button onClick={handleOpenModal} leftIcon={<i className="fas fa-star mr-2"></i>}>添加评分</Button>}>
      <Table columns={columns} data={filteredScores} keyExtractor={s => s.id} />
      <Modal isOpen={isModalOpen} onClose={handleCloseModal} title="添加文明宿舍评分">
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="roomId" className="block text-sm font-medium text-gray-700 mb-1">房间</label>
            <select
              id="roomId"
              name="roomId"
              value={newScore.roomId || ''}
              onChange={handleInputChange}
              className="block w-full px-3 py-2 border border-neutral-DEFAULT rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
              required
            >
              <option value="">-- 选择房间 --</option>
              {roomsInAdminBuilding.map(r => <option key={r.id} value={r.id}>{r.roomNumber}</option>)}
            </select>
          </div>
          <Input name="date" label="评比日期" type="date" value={newScore.date || ''} onChange={handleInputChange} required />
          <Input name="score" label="分数 (0-100)" type="number" value={newScore.score || ''} onChange={handleInputChange} required min="0" max="100" />
          <div>
            <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-1">备注 (可选)</label>
            <textarea id="notes" name="notes" value={newScore.notes || ''} onChange={handleInputChange} rows={3} className="block w-full px-3 py-2 border border-neutral-DEFAULT rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm" />
          </div>
          <div className="flex justify-end space-x-2 pt-4">
            <Button type="button" variant="ghost" onClick={handleCloseModal}>取消</Button>
            <Button type="submit">保存评分</Button>
          </div>
        </form>
      </Modal>
    </Card>
  );
};

export default CivilizedDormPage;
