import mysql from 'mysql2/promise';

const fixZhangsan = async () => {
  let connection;
  
  try {
    connection = await mysql.createConnection({
      host: 'localhost',
      port: 3306,
      user: 'root',
      password: 'root',
      database: 'dorm_management',
      charset: 'utf8mb4'
    });

    console.log('🔧 修复张三的宿舍楼分配...\n');

    // 1. 查看当前状态
    console.log('📋 当前状态:');
    const [currentState] = await connection.execute(`
      SELECT u.id, u.name, u.email, u.dorm_building_id, ur.role_name
      FROM users u 
      LEFT JOIN user_roles ur ON u.role_id = ur.id
      WHERE u.name = '张三' OR u.email = '<EMAIL>'
    `);

    if (currentState.length > 0) {
      const user = currentState[0];
      console.log(`   用户: ${user.name} (${user.email})`);
      console.log(`   ID: ${user.id}`);
      console.log(`   角色: ${user.role_name}`);
      console.log(`   当前分配宿舍楼ID: ${user.dorm_building_id || '无'}`);
    }

    // 2. 查看宿舍楼状态
    console.log('\n🏢 宿舍楼状态:');
    const [buildings] = await connection.execute(`
      SELECT * FROM dorm_buildings ORDER BY created_at
    `);

    buildings.forEach((building, index) => {
      console.log(`   ${index + 1}. ${building.name} (ID: ${building.id})`);
      console.log(`      分配管理员ID: ${building.assigned_admin_id || '无'}`);
    });

    // 3. 修复分配 - 将张三分配给A栋
    console.log('\n🔧 开始修复...');
    
    // 找到张三的用户ID
    const [zhangsan] = await connection.execute(`
      SELECT id FROM users WHERE name = '张三' OR email = '<EMAIL>'
    `);

    if (zhangsan.length === 0) {
      console.log('❌ 找不到张三的用户记录');
      return;
    }

    const zhangsanId = zhangsan[0].id;
    console.log(`   张三的用户ID: ${zhangsanId}`);

    // 找到A栋的ID
    const [buildingA] = await connection.execute(`
      SELECT id FROM dorm_buildings WHERE name LIKE '%A%' OR id = 'bldgA'
    `);

    if (buildingA.length === 0) {
      console.log('❌ 找不到A栋记录');
      return;
    }

    const buildingAId = buildingA[0].id;
    console.log(`   A栋的ID: ${buildingAId}`);

    // 双向分配
    // 1. 在用户表中设置张三的dorm_building_id
    await connection.execute(`
      UPDATE users SET dorm_building_id = ? WHERE id = ?
    `, [buildingAId, zhangsanId]);

    // 2. 在宿舍楼表中设置A栋的assigned_admin_id
    await connection.execute(`
      UPDATE dorm_buildings SET assigned_admin_id = ? WHERE id = ?
    `, [zhangsanId, buildingAId]);

    console.log('✅ 分配完成！');

    // 4. 验证修复结果
    console.log('\n✅ 验证修复结果:');
    const [verifyUser] = await connection.execute(`
      SELECT u.*, db.name as building_name
      FROM users u 
      LEFT JOIN dorm_buildings db ON u.dorm_building_id = db.id
      WHERE u.id = ?
    `, [zhangsanId]);

    const [verifyBuilding] = await connection.execute(`
      SELECT db.*, u.name as admin_name
      FROM dorm_buildings db
      LEFT JOIN users u ON db.assigned_admin_id = u.id
      WHERE db.id = ?
    `, [buildingAId]);

    if (verifyUser.length > 0) {
      const user = verifyUser[0];
      console.log(`   用户: ${user.name}`);
      console.log(`   分配宿舍楼: ${user.building_name}`);
    }

    if (verifyBuilding.length > 0) {
      const building = verifyBuilding[0];
      console.log(`   宿舍楼: ${building.name}`);
      console.log(`   分配管理员: ${building.admin_name}`);
    }

    console.log('\n🎉 修复完成！张三现在应该可以管理A栋的房间了。');

  } catch (error) {
    console.error('❌ 修复失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
};

fixZhangsan();
