import mysql from 'mysql2/promise';

async function insertVisitorData() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'root',
    database: 'dorm_management'
  });

  try {
    console.log('👥 插入访客记录测试数据...');
    
    // 检查访客记录表是否存在
    const [tables] = await connection.execute("SHOW TABLES LIKE 'visitors'");
    if (tables.length === 0) {
      console.log('⚠️ 访客记录表不存在，正在创建...');
      
      // 创建访客记录表
      await connection.execute(`
        CREATE TABLE visitors (
          id VARCHAR(50) PRIMARY KEY,
          visitor_name VARCHAR(100) NOT NULL,
          visitor_id_number VARCHAR(50),
          reason VARCHAR(200),
          entry_time TIMESTAMP NOT NULL,
          exit_time TIMESTAMP NULL,
          visited_student_id VARCHAR(50) NOT NULL,
          dorm_building_id VARCHAR(50) NOT NULL,
          recorded_by VARCHAR(50) NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
          FOREIGN KEY (visited_student_id) REFERENCES users(id) ON DELETE CASCADE,
          FOREIGN KEY (dorm_building_id) REFERENCES dorm_buildings(id) ON DELETE CASCADE,
          FOREIGN KEY (recorded_by) REFERENCES users(id) ON DELETE CASCADE
        )
      `);
      console.log('✅ 访客记录表创建成功');
    } else {
      console.log('✅ 访客记录表已存在');
    }
    
    // 清空现有数据
    await connection.execute('DELETE FROM visitors');
    console.log('🗑️ 清空现有访客记录数据');
    
    // 插入测试访客记录数据
    const visitors = [
      {
        id: 'vis001',
        visitor_name: '张父',
        visitor_id_number: '123456789012345678',
        reason: '探望孩子',
        entry_time: '2024-07-20 14:00:00',
        exit_time: '2024-07-20 16:00:00',
        visited_student_id: 'student01', // 王五
        dorm_building_id: 'bldgA',
        recorded_by: 'dormadmin01' // 张三
      },
      {
        id: 'vis002',
        visitor_name: '李明',
        visitor_id_number: '987654321098765432',
        reason: '送生活用品',
        entry_time: '2024-07-21 10:00:00',
        exit_time: null, // 未离开
        visited_student_id: 'student03', // 孙七
        dorm_building_id: 'bldgA',
        recorded_by: 'dormadmin01' // 张三
      },
      {
        id: 'vis003',
        visitor_name: '王同学',
        visitor_id_number: '456789123456789012',
        reason: '同学聚会',
        entry_time: '2024-07-22 15:30:00',
        exit_time: '2024-07-22 18:00:00',
        visited_student_id: 'student04', // 周八
        dorm_building_id: 'bldgA',
        recorded_by: 'dormadmin01' // 张三
      },
      {
        id: 'vis004',
        visitor_name: '刘老师',
        visitor_id_number: '321654987321654987',
        reason: '学术讨论',
        entry_time: '2024-07-23 09:00:00',
        exit_time: '2024-07-23 11:30:00',
        visited_student_id: 'student05', // 吴九
        dorm_building_id: 'bldgA',
        recorded_by: 'dormadmin01' // 张三
      },
      {
        id: 'vis005',
        visitor_name: '快递员',
        visitor_id_number: '159753486159753486',
        reason: '送快递',
        entry_time: '2024-07-24 16:20:00',
        exit_time: null, // 未离开
        visited_student_id: 'student06', // 郑十
        dorm_building_id: 'bldgA',
        recorded_by: 'dormadmin01' // 张三
      }
    ];
    
    for (const visitor of visitors) {
      try {
        await connection.execute(`
          INSERT INTO visitors (id, visitor_name, visitor_id_number, reason, entry_time, exit_time, visited_student_id, dorm_building_id, recorded_by)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          visitor.id,
          visitor.visitor_name,
          visitor.visitor_id_number,
          visitor.reason,
          visitor.entry_time,
          visitor.exit_time,
          visitor.visited_student_id,
          visitor.dorm_building_id,
          visitor.recorded_by
        ]);
        console.log(`✅ 添加访客记录: ${visitor.id} - ${visitor.visitor_name}`);
      } catch (error) {
        console.log(`❌ 添加失败: ${visitor.id} - ${error.message}`);
      }
    }
    
    // 查看结果
    console.log('\n📊 查看A栋访客记录:');
    const [results] = await connection.execute(`
      SELECT v.*, u.name as visited_student_name, u.room_number
      FROM visitors v
      LEFT JOIN users u ON v.visited_student_id = u.id
      WHERE v.dorm_building_id = 'bldgA'
      ORDER BY v.entry_time DESC
    `);
    
    console.log(`A栋访客记录总数: ${results.length}`);
    results.forEach(v => {
      console.log(`  - ${v.visitor_name} 访问 ${v.visited_student_name} - 进入: ${v.entry_time} 离开: ${v.exit_time || '未离开'}`);
    });
    
    await connection.end();
    console.log('\n🎉 访客记录数据添加完成！');
  } catch (error) {
    console.error('❌ 操作失败:', error);
    await connection.end();
    process.exit(1);
  }
}

insertVisitorData();
