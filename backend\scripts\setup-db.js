import mysql from 'mysql2/promise';
import bcrypt from 'bcrypt';
import dotenv from 'dotenv';

dotenv.config();

const setupDatabase = async () => {
  let connection;
  
  try {
    console.log('🔄 开始设置数据库...');
    
    // 连接到MySQL服务器（不指定数据库）
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || 'root',
      charset: 'utf8mb4'
    });

    console.log('✅ 成功连接到MySQL服务器');

    // 创建数据库
    await connection.execute(`CREATE DATABASE IF NOT EXISTS dorm_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`);
    console.log('✅ 数据库创建成功');

    // 使用数据库
    await connection.execute(`USE dorm_management`);

    // 删除现有表（如果存在）
    const tables = ['repair_requests', 'users', 'majors', 'colleges', 'dorm_buildings', 'user_roles'];
    for (const table of tables) {
      await connection.execute(`DROP TABLE IF EXISTS ${table}`);
    }
    console.log('✅ 清理旧表完成');

    // 创建用户角色表
    await connection.execute(`
      CREATE TABLE user_roles (
        id INT PRIMARY KEY AUTO_INCREMENT,
        role_name VARCHAR(50) NOT NULL UNIQUE,
        role_description TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);

    // 创建学院表
    await connection.execute(`
      CREATE TABLE colleges (
        id VARCHAR(50) PRIMARY KEY,
        name VARCHAR(100) NOT NULL UNIQUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);

    // 创建专业表
    await connection.execute(`
      CREATE TABLE majors (
        id VARCHAR(50) PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        college_id VARCHAR(50) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (college_id) REFERENCES colleges(id) ON DELETE CASCADE
      )
    `);

    // 创建宿舍楼表
    await connection.execute(`
      CREATE TABLE dorm_buildings (
        id VARCHAR(50) PRIMARY KEY,
        name VARCHAR(100) NOT NULL UNIQUE,
        floors INT NOT NULL,
        total_rooms INT NOT NULL,
        assigned_admin_id VARCHAR(50),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);

    // 创建用户表
    await connection.execute(`
      CREATE TABLE users (
        id VARCHAR(50) PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        email VARCHAR(150) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        phone VARCHAR(20),
        role_id INT NOT NULL,
        college_id VARCHAR(50),
        major_id VARCHAR(50),
        dorm_building_id VARCHAR(50),
        room_number VARCHAR(20),
        emergency_contact_name VARCHAR(100),
        emergency_contact_phone VARCHAR(20),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (role_id) REFERENCES user_roles(id) ON DELETE RESTRICT,
        FOREIGN KEY (college_id) REFERENCES colleges(id) ON DELETE SET NULL,
        FOREIGN KEY (major_id) REFERENCES majors(id) ON DELETE SET NULL,
        FOREIGN KEY (dorm_building_id) REFERENCES dorm_buildings(id) ON DELETE SET NULL
      )
    `);

    // 创建维修请求表
    await connection.execute(`
      CREATE TABLE repair_requests (
        id VARCHAR(50) PRIMARY KEY,
        student_id VARCHAR(50) NOT NULL,
        room_number VARCHAR(20),
        dorm_building_id VARCHAR(50),
        description TEXT NOT NULL,
        status ENUM('待处理', '已指派', '维修中', '已完成', '已确认', '已取消') DEFAULT '待处理',
        assigned_staff_id VARCHAR(50),
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (dorm_building_id) REFERENCES dorm_buildings(id) ON DELETE SET NULL,
        FOREIGN KEY (assigned_staff_id) REFERENCES users(id) ON DELETE SET NULL
      )
    `);

    console.log('✅ 表结构创建完成');

    // 生成密码哈希
    const passwordHash = await bcrypt.hash('password123', 10);

    // 插入角色数据
    await connection.execute(`
      INSERT INTO user_roles (id, role_name, role_description) VALUES
      (1, '系统管理员', '系统管理员，拥有所有权限'),
      (2, '宿舍管理员', '宿舍管理员，管理特定宿舍楼'),
      (3, '学生', '学生用户'),
      (4, '维修人员', '维修人员，处理维修请求')
    `);

    // 插入学院数据
    await connection.execute(`
      INSERT INTO colleges (id, name) VALUES
      ('college01', '计算机科学学院'),
      ('college02', '电子工程学院'),
      ('college03', '机械工程学院'),
      ('college04', '经济管理学院')
    `);

    // 插入专业数据
    await connection.execute(`
      INSERT INTO majors (id, name, college_id) VALUES
      ('major01', '计算机科学与技术', 'college01'),
      ('major02', '软件工程', 'college01'),
      ('major03', '电子信息工程', 'college02'),
      ('major04', '通信工程', 'college02')
    `);

    // 插入宿舍楼数据
    await connection.execute(`
      INSERT INTO dorm_buildings (id, name, floors, total_rooms) VALUES
      ('bldgA', 'A栋', 6, 120),
      ('bldgB', 'B栋', 8, 160),
      ('bldgC', 'C栋', 5, 100)
    `);

    // 插入用户数据
    await connection.execute(`
      INSERT INTO users (id, name, email, password, role_id, phone, college_id, major_id, dorm_building_id, emergency_contact_name, emergency_contact_phone) VALUES
      (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?),
      (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?),
      (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?),
      (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?),
      (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?),
      (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      'sysadmin01', '系统管理员', '<EMAIL>', passwordHash, 1, '13800000001', null, null, null, null, null,
      'repair01', '爱德华·修理工', '<EMAIL>', passwordHash, 4, '13800000005', null, null, null, null, null,
      'dormadmin01', '张三', '<EMAIL>', passwordHash, 2, '13800000002', null, null, 'bldgA', null, null,
      'dormadmin02', '李四', '<EMAIL>', passwordHash, 2, '13800000003', null, null, 'bldgB', null, null,
      'student01', '王五', '<EMAIL>', passwordHash, 3, '13800000004', 'college01', 'major01', 'bldgA', '王父', '13900000001',
      'student02', '赵六', '<EMAIL>', passwordHash, 3, '13800000006', 'college02', 'major03', 'bldgB', '赵母', '13900000002'
    ]);

    // 插入维修请求数据
    await connection.execute(`
      INSERT INTO repair_requests (id, student_id, room_number, dorm_building_id, description, status, assigned_staff_id, notes) VALUES
      ('repair001', 'student01', '101', 'bldgA', '水龙头漏水', '待处理', NULL, NULL),
      ('repair002', 'student02', '205', 'bldgB', '灯泡坏了', '已指派', 'repair01', '已安排维修人员')
    `);

    console.log('✅ 测试数据插入完成');

    // 验证数据
    const [userCount] = await connection.execute('SELECT COUNT(*) as count FROM users');
    const [collegeCount] = await connection.execute('SELECT COUNT(*) as count FROM colleges');
    const [repairCount] = await connection.execute('SELECT COUNT(*) as count FROM repair_requests');

    console.log('\n📊 数据统计:');
    console.log(`   ✅ 用户数量: ${userCount[0].count}`);
    console.log(`   ✅ 学院数量: ${collegeCount[0].count}`);
    console.log(`   ✅ 维修请求数量: ${repairCount[0].count}`);

    console.log('\n🎉 数据库设置完成！');
    console.log('\n🔑 测试账户 (密码: password123):');
    console.log('   📧 系统管理员: <EMAIL>');
    console.log('   📧 宿舍管理员: <EMAIL>');
    console.log('   📧 学生: <EMAIL>');
    console.log('   📧 维修人员: <EMAIL>');
    
  } catch (error) {
    console.error('❌ 数据库设置失败:', error.message);
    console.error('错误详情:', error);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
};

setupDatabase();
