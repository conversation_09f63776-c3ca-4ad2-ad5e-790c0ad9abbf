
import React, { useState, useEffect, FormEvent } from 'react';
import { Violation, User, UserRole } from '../../types';
import { useAuth } from '../../contexts/AuthContext';
import Table from '../../components/Table';
import Card from '../../components/Card';
import Button from '../../components/Button';
import Modal from '../../components/Modal';
import Input from '../../components/Input';

const API_BASE_URL = 'http://localhost:3002/api';

const ViolationRecordPage: React.FC = () => {
  const { currentUser } = useAuth();
  const [violations, setViolations] = useState<any[]>([]);
  const [students, setStudents] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [adminBuildingId, setAdminBuildingId] = useState<string>('');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [newViolation, setNewViolation] = useState<any>({});

  // 获取宿舍楼信息
  const fetchBuildingInfo = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_BASE_URL}/dorm-buildings`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          const userBuilding = data.data.find((building: any) =>
            building.assigned_admin_id === currentUser?.id
          );
          if (userBuilding) {
            setAdminBuildingId(userBuilding.id);
          } else {
            setIsLoading(false);
          }
        }
      }
    } catch (error) {
      console.error('获取宿舍楼信息错误:', error);
      setIsLoading(false);
    }
  };

  // 获取违规记录列表
  const fetchViolations = async () => {
    if (!adminBuildingId) return;

    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_BASE_URL}/violations?building_id=${adminBuildingId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setViolations(data.data);
        }
      }
    } catch (error) {
      console.error('获取违规记录错误:', error);
    }
  };

  // 获取学生列表
  const fetchStudents = async () => {
    if (!adminBuildingId) return;

    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_BASE_URL}/student-allocation/students?building_id=${adminBuildingId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setStudents(data.data);
        }
      }
    } catch (error) {
      console.error('获取学生列表错误:', error);
    }
  };

  useEffect(() => {
    if (currentUser) {
      fetchBuildingInfo();
    }
  }, [currentUser]);

  useEffect(() => {
    if (adminBuildingId) {
      Promise.all([fetchViolations(), fetchStudents()]).then(() => {
        setIsLoading(false);
      });
    }
  }, [adminBuildingId]);

  if (!currentUser || currentUser.role !== UserRole.DORM_ADMIN) return <p>权限不足。</p>;

  if (isLoading) {
    return (
      <Card title="违规记录管理">
        <div className="flex justify-center items-center py-8">
          <div className="text-gray-500">加载中...</div>
        </div>
      </Card>
    );
  }

  if (!adminBuildingId) {
    return (
      <Card title="违规记录管理">
        <div className="p-8 text-center">
          <div className="text-red-500 mb-4">
            <i className="fas fa-exclamation-triangle text-4xl mb-3"></i>
            <h3 className="text-lg font-semibold">未分配宿舍楼</h3>
          </div>
          <p className="text-gray-600">您还没有被分配管理任何宿舍楼，请联系系统管理员进行分配。</p>
        </div>
      </Card>
    );
  }

  const buildingName = currentUser.dormBuilding || "未知楼栋";

  const columns = [
    { header: '学生姓名', accessor: 'student_name' as keyof any },
    { header: '日期', accessor: 'date' as keyof any },
    { header: '违规类型', accessor: 'type' as keyof any },
    { header: '描述', accessor: 'description' as keyof any, render: (v: any) => <span title={v.description}>{v.description.substring(0,30)}...</span> },
    { header: '处理措施', accessor: (v: any) => v.action_taken || "N/A" },
    {
      header: '操作',
      accessor: 'id' as keyof any,
      render: (violation: any) => (
        <Button size="sm" variant="danger" onClick={() => handleDelete(violation.id)}><i className="fas fa-trash"></i></Button>
      )
    }
  ];

  const handleOpenModal = () => {
    setNewViolation({
      recorded_by: currentUser.id,
      dorm_building_id: adminBuildingId,
      date: new Date().toISOString().split('T')[0]
    });
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setNewViolation({});
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    if (name === "student_id") {
        const student = students.find(s => s.id === value);
        setNewViolation(prev => ({ ...prev, student_id: value, student_name: student?.name }));
    } else {
        setNewViolation(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (newViolation.student_id && newViolation.type && newViolation.description) {
      try {
        const token = localStorage.getItem('token');
        const response = await fetch(`${API_BASE_URL}/violations`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            student_id: newViolation.student_id,
            dorm_building_id: adminBuildingId,
            date: newViolation.date,
            type: newViolation.type,
            description: newViolation.description,
            action_taken: newViolation.action_taken,
          }),
        });

        if (response.ok) {
          const data = await response.json();
          if (data.success) {
            // 重新获取违规记录列表
            await fetchViolations();
            handleCloseModal();
            alert('违规记录添加成功！');
          } else {
            alert(data.message || '添加失败');
          }
        } else {
          const errorData = await response.json();
          alert(errorData.message || '添加失败');
        }
      } catch (error) {
        console.error('添加违规记录错误:', error);
        alert('添加失败，请重试');
      }
    } else {
        alert("请填写所有必填项。");
    }
  };

  const handleDelete = async (id: string) => {
    if (window.confirm("确定删除此违规记录吗?")) {
      try {
        const token = localStorage.getItem('token');
        const response = await fetch(`${API_BASE_URL}/violations/${id}`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${token}`,
          },
        });

        if (response.ok) {
          const data = await response.json();
          if (data.success) {
            // 重新获取违规记录列表
            await fetchViolations();
            alert('违规记录删除成功！');
          } else {
            alert(data.message || '删除失败');
          }
        } else {
          const errorData = await response.json();
          alert(errorData.message || '删除失败');
        }
      } catch (error) {
        console.error('删除违规记录错误:', error);
        alert('删除失败，请重试');
      }
    }
  }

  return (
    <Card title={`${buildingName} - 违规记录管理`} actions={<Button onClick={handleOpenModal} leftIcon={<i className="fas fa-plus mr-2"></i>}>添加记录</Button>}>
      <Table columns={columns} data={violations} keyExtractor={v => v.id} />
      <Modal isOpen={isModalOpen} onClose={handleCloseModal} title="添加违规记录">
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="student_id" className="block text-sm font-medium text-gray-700 mb-1">学生</label>
            <select
              id="student_id"
              name="student_id"
              value={newViolation.student_id || ''}
              onChange={handleInputChange}
              className="block w-full px-3 py-2 border border-neutral-DEFAULT rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
              required
            >
              <option value="">-- 选择学生 --</option>
              {students.map(s => <option key={s.id} value={s.id}>{s.name} ({s.room_number || '未分配'})</option>)}
            </select>
          </div>
          <Input name="date" label="日期" type="date" value={newViolation.date || ''} onChange={handleInputChange} required />
          <Input name="type" label="违规类型" value={newViolation.type || ''} onChange={handleInputChange} placeholder="例如：使用违禁电器、晚归" required />
          <div>
            <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">详细描述</label>
            <textarea id="description" name="description" value={newViolation.description || ''} onChange={handleInputChange} rows={3} className="block w-full px-3 py-2 border border-neutral-DEFAULT rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm" required />
          </div>
          <Input name="action_taken" label="处理措施 (可选)" value={newViolation.action_taken || ''} onChange={handleInputChange} />
          <div className="flex justify-end space-x-2 pt-4">
            <Button type="button" variant="ghost" onClick={handleCloseModal}>取消</Button>
            <Button type="submit">添加记录</Button>
          </div>
        </form>
      </Modal>
    </Card>
  );
};

export default ViolationRecordPage;
