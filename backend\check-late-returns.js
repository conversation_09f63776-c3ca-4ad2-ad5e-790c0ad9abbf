import mysql from 'mysql2/promise';

async function checkLateReturns() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'root',
    database: 'dorm_management'
  });

  try {
    console.log('🌙 检查晚归记录数据...');
    
    // 检查晚归记录表是否存在
    const [tables] = await connection.execute("SHOW TABLES LIKE 'late_returns'");
    console.log(`晚归记录表存在: ${tables.length > 0 ? '是' : '否'}`);
    
    if (tables.length === 0) {
      console.log('⚠️ 晚归记录表不存在，正在创建...');
      
      // 创建晚归记录表
      await connection.execute(`
        CREATE TABLE late_returns (
          id VARCHAR(50) PRIMARY KEY,
          student_id VARCHAR(50) NOT NULL,
          dorm_building_id VARCHAR(50) NOT NULL,
          date DATE NOT NULL,
          time TIME NOT NULL,
          reason TEXT,
          recorded_by VARCHAR(50) NOT NULL,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
      `);
      console.log('✅ 晚归记录表创建成功');
    }
    
    // 检查现有数据
    const [existing] = await connection.execute('SELECT COUNT(*) as count FROM late_returns');
    console.log(`现有晚归记录数量: ${existing[0].count}`);
    
    if (existing[0].count === 0) {
      console.log('📝 插入测试晚归记录数据...');
      
      // 插入测试数据
      const lateReturns = [
        ['late001', 'student01', 'bldgA', '2024-07-18', '23:30:00', '图书馆学习到很晚', 'dormadmin01'],
        ['late002', 'student03', 'bldgA', '2024-07-19', '00:15:00', '社团活动结束较晚', 'dormadmin01'],
        ['late003', 'student04', 'bldgA', '2024-07-20', '23:45:00', '实验室项目加班', 'dormadmin01'],
        ['late004', 'student01', 'bldgA', '2024-07-21', '00:30:00', '朋友聚会', 'dormadmin01'],
        ['late005', 'student05', 'bldgA', '2024-07-22', '23:50:00', '兼职工作结束晚', 'dormadmin01']
      ];
      
      for (const lr of lateReturns) {
        try {
          await connection.execute(`
            INSERT INTO late_returns (id, student_id, dorm_building_id, date, time, reason, recorded_by)
            VALUES (?, ?, ?, ?, ?, ?, ?)
          `, lr);
          console.log(`✅ 插入晚归记录: ${lr[0]}`);
        } catch (error) {
          console.log(`❌ 插入失败: ${lr[0]} - ${error.message}`);
        }
      }
    }
    
    // 查看A栋晚归记录
    console.log('\n📊 A栋晚归记录:');
    const [results] = await connection.execute(`
      SELECT lr.*, u.name as student_name
      FROM late_returns lr
      LEFT JOIN users u ON lr.student_id = u.id
      WHERE lr.dorm_building_id = 'bldgA'
      ORDER BY lr.date DESC, lr.time DESC
    `);
    
    console.log(`A栋晚归记录总数: ${results.length}`);
    results.forEach(lr => {
      console.log(`  - ${lr.student_name || lr.student_id} - ${lr.date} ${lr.time} - ${lr.reason}`);
    });
    
    await connection.end();
    console.log('\n🎉 检查完成！');
  } catch (error) {
    console.error('❌ 操作失败:', error);
    await connection.end();
    process.exit(1);
  }
}

checkLateReturns();
