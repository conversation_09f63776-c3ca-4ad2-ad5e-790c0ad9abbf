import mysql from 'mysql2/promise';

async function checkBuildingAssignment() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'root',
    database: 'dorm_management'
  });

  try {
    console.log('🔍 检查宿舍楼分配情况...');
    
    // 检查张三的信息
    console.log('\n👤 检查张三信息:');
    const [zhangsan] = await connection.execute('SELECT * FROM users WHERE email = "<EMAIL>"');
    if (zhangsan.length > 0) {
      const user = zhangsan[0];
      console.log('张三信息:', {
        id: user.id,
        name: user.name,
        email: user.email,
        role_id: user.role_id,
        dorm_building_id: user.dorm_building_id
      });
    }
    
    // 检查宿舍楼信息
    console.log('\n🏢 检查宿舍楼信息:');
    const [buildings] = await connection.execute('SELECT * FROM dorm_buildings');
    buildings.forEach(b => {
      console.log(`宿舍楼: ${b.name} (${b.id})`);
      console.log(`  - 管理员ID: ${b.assigned_admin_id}`);
      console.log(`  - 楼层数: ${b.floors}`);
      console.log(`  - 房间数: ${b.total_rooms}`);
    });
    
    // 修复宿舍楼分配
    console.log('\n🔧 修复宿舍楼分配...');
    await connection.execute(`
      UPDATE dorm_buildings 
      SET assigned_admin_id = 'dormadmin01' 
      WHERE id = 'bldgA'
    `);
    
    await connection.execute(`
      UPDATE dorm_buildings 
      SET assigned_admin_id = 'dormadmin02' 
      WHERE id = 'bldgB'
    `);
    
    console.log('✅ 宿舍楼分配已修复');
    
    // 验证修复结果
    console.log('\n📊 验证修复结果:');
    const [updatedBuildings] = await connection.execute('SELECT * FROM dorm_buildings');
    updatedBuildings.forEach(b => {
      console.log(`宿舍楼: ${b.name} (${b.id}) - 管理员: ${b.assigned_admin_id}`);
    });
    
    await connection.end();
    console.log('\n🎉 检查完成！');
  } catch (error) {
    console.error('❌ 检查失败:', error);
    await connection.end();
    process.exit(1);
  }
}

checkBuildingAssignment();
