import mysql from 'mysql2/promise';
import bcrypt from 'bcrypt';

async function insertStudents() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'root',
    database: 'dorm_management'
  });

  try {
    console.log('🚀 开始插入4个学生到A栋...');
    
    // 生成密码哈希
    const passwordHash = await bcrypt.hash('password123', 10);
    
    // 要插入的4个学生
    const students = [
      { id: 'student03', name: '孙七', email: '<EMAIL>' },
      { id: 'student04', name: '周八', email: '<EMAIL>' },
      { id: 'student05', name: '吴九', email: '<EMAIL>' },
      { id: 'student06', name: '郑十', email: '<EMAIL>' }
    ];
    
    console.log('\n📝 插入学生数据...');
    for (let i = 0; i < students.length; i++) {
      const student = students[i];
      try {
        await connection.execute(`
          INSERT INTO users (
            id, name, email, password, role_id, phone, 
            college_id, major_id, dorm_building_id, 
            emergency_contact_name, emergency_contact_phone
          ) VALUES (?, ?, ?, ?, 3, ?, 'college01', 'major01', 'bldgA', ?, ?)
        `, [
          student.id, 
          student.name, 
          student.email, 
          passwordHash,
          '1380000000' + (7 + i), // 手机号：13800000007, 13800000008, 等
          student.name.slice(0, 1) + '父', // 紧急联系人
          '1390000000' + (7 + i) // 紧急联系人电话
        ]);
        console.log(`✅ 成功插入: ${student.name} (${student.id})`);
      } catch (error) {
        if (error.code === 'ER_DUP_ENTRY') {
          console.log(`⚠️ 学生已存在: ${student.name} (${student.id})`);
        } else {
          console.log(`❌ 插入失败: ${student.name} - ${error.message}`);
        }
      }
    }
    
    // 查看A栋的所有学生
    console.log('\n📊 查看A栋学生列表:');
    const [students_result] = await connection.execute(`
      SELECT id, name, email, dorm_building_id, room_number 
      FROM users 
      WHERE role_id = 3 AND dorm_building_id = 'bldgA' 
      ORDER BY name
    `);
    
    console.log(`A栋学生总数: ${students_result.length}`);
    students_result.forEach(s => {
      console.log(`  - ${s.name} (${s.id}) - 房间: ${s.room_number || '未分配'}`);
    });
    
    // 查看总用户数
    const [total_users] = await connection.execute('SELECT COUNT(*) as count FROM users');
    console.log(`\n📈 系统总用户数: ${total_users[0].count}`);
    
    await connection.end();
    console.log('\n🎉 学生数据插入完成！');
  } catch (error) {
    console.error('❌ 操作失败:', error);
    await connection.end();
    process.exit(1);
  }
}

insertStudents();
