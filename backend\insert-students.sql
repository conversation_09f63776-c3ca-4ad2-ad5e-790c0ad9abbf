-- 插入4个学生到A栋：孙七、周八、吴九、郑十
-- 密码哈希是 'password123' 的bcrypt哈希值

INSERT INTO users (
    id, 
    name, 
    email, 
    password, 
    role_id, 
    phone, 
    college_id, 
    major_id, 
    dorm_building_id, 
    emergency_contact_name, 
    emergency_contact_phone
) VALUES 
(
    'student03', 
    '孙七', 
    '<EMAIL>', 
    '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 
    3, 
    '13800000007', 
    'college01', 
    'major01', 
    'bldgA', 
    '孙父', 
    '13900000007'
),
(
    'student04', 
    '周八', 
    '<EMAIL>', 
    '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 
    3, 
    '13800000008', 
    'college01', 
    'major01', 
    'bldgA', 
    '周父', 
    '13900000008'
),
(
    'student05', 
    '吴九', 
    '<EMAIL>', 
    '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 
    3, 
    '13800000009', 
    'college01', 
    'major01', 
    'bldgA', 
    '吴父', 
    '13900000009'
),
(
    'student06', 
    '郑十', 
    '<EMAIL>', 
    '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 
    3, 
    '13800000010', 
    'college01', 
    'major01', 
    'bldgA', 
    '郑父', 
    '13900000010'
);

-- 查看插入结果
SELECT 
    id, 
    name, 
    email, 
    dorm_building_id, 
    room_number 
FROM users 
WHERE role_id = 3 AND dorm_building_id = 'bldgA' 
ORDER BY name;
