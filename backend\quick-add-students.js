import mysql from 'mysql2/promise';
import bcrypt from 'bcrypt';

async function quickAddStudents() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'root',
    database: 'dorm_management'
  });

  try {
    console.log('🚀 快速添加学生数据...');
    
    // 生成密码哈希
    const passwordHash = await bcrypt.hash('password123', 10);
    
    // 添加4个学生到A栋
    const students = [
      { id: 'student03', name: '孙七', email: '<EMAIL>' },
      { id: 'student04', name: '周八', email: '<EMAIL>' },
      { id: 'student05', name: '吴九', email: '<EMAIL>' },
      { id: 'student06', name: '郑十', email: '<EMAIL>' }
    ];
    
    for (const student of students) {
      try {
        await connection.execute(`
          INSERT INTO users (id, name, email, password, role_id, phone, college_id, major_id, dorm_building_id, emergency_contact_name, emergency_contact_phone) 
          VALUES (?, ?, ?, ?, 3, ?, 'college01', 'major01', 'bldgA', ?, ?)
        `, [
          student.id, 
          student.name, 
          student.email, 
          passwordHash,
          '138' + String(Math.floor(Math.random() * 100000000)).padStart(8, '0'),
          student.name.slice(0, 1) + '父',
          '139' + String(Math.floor(Math.random() * 100000000)).padStart(8, '0')
        ]);
        console.log(`✅ 添加学生: ${student.name} -> A栋`);
      } catch (error) {
        if (error.code === 'ER_DUP_ENTRY') {
          console.log(`⚠️ 学生已存在: ${student.name}`);
        } else {
          console.log(`❌ 添加失败: ${student.name} - ${error.message}`);
        }
      }
    }
    
    // 检查结果
    const [result] = await connection.execute(`
      SELECT id, name, dorm_building_id, room_number 
      FROM users 
      WHERE role_id = 3 AND dorm_building_id = 'bldgA' 
      ORDER BY name
    `);
    
    console.log(`\n📊 A栋学生总数: ${result.length}`);
    result.forEach(s => {
      console.log(`  - ${s.name} (${s.id}) - 房间: ${s.room_number || '未分配'}`);
    });
    
    await connection.end();
    console.log('\n🎉 学生数据添加完成！');
  } catch (error) {
    console.error('❌ 操作失败:', error);
    await connection.end();
    process.exit(1);
  }
}

quickAddStudents();
