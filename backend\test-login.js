// 测试登录API
const testLogin = async () => {
  try {
    console.log('🔄 测试登录API...');
    
    const response = await fetch('http://localhost:3002/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password123',
        role: '系统管理员'
      }),
    });

    console.log('响应状态:', response.status);
    console.log('响应头:', Object.fromEntries(response.headers.entries()));

    const data = await response.json();
    console.log('响应数据:', JSON.stringify(data, null, 2));

    if (data.success) {
      console.log('✅ 登录API测试成功！');
    } else {
      console.log('❌ 登录API测试失败:', data.message);
    }
  } catch (error) {
    console.error('❌ 请求失败:', error.message);
  }
};

// 使用动态导入来支持fetch
import('node-fetch').then(({ default: fetch }) => {
  global.fetch = fetch;
  testLogin();
}).catch(() => {
  // 如果没有node-fetch，使用原生fetch（Node.js 18+）
  testLogin();
});
