import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

dotenv.config();

const initDatabase = async () => {
  let connection;
  
  try {
    console.log('🔄 开始初始化数据库...');
    
    // 连接到MySQL服务器
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || 'root',
      database: process.env.DB_NAME || 'dorm_management',
      charset: 'utf8mb4'
    });

    console.log('✅ 成功连接到数据库');

    // 创建学院表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS colleges (
        id VARCHAR(50) PRIMARY KEY,
        name VARCHAR(100) NOT NULL UNIQUE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ 学院表创建成功');

    // 创建专业表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS majors (
        id VARCHAR(50) PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        college_id VARCHAR(50) NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (college_id) REFERENCES colleges(id) ON DELETE CASCADE,
        UNIQUE KEY unique_major_per_college (name, college_id)
      )
    `);
    console.log('✅ 专业表创建成功');

    // 创建宿舍楼表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS dorm_buildings (
        id VARCHAR(50) PRIMARY KEY,
        name VARCHAR(100) NOT NULL UNIQUE,
        floors INT NOT NULL,
        total_rooms INT NOT NULL,
        assigned_admin_id VARCHAR(50),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);
    console.log('✅ 宿舍楼表创建成功');

    // 创建用户表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS users (
        id VARCHAR(50) PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        email VARCHAR(150) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        phone VARCHAR(20),
        role ENUM('系统管理员', '宿舍管理员', '学生', '维修人员') NOT NULL,
        college_id VARCHAR(50),
        major_id VARCHAR(50),
        dorm_building_id VARCHAR(50),
        room_number VARCHAR(20),
        emergency_contact_name VARCHAR(100),
        emergency_contact_phone VARCHAR(20),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (college_id) REFERENCES colleges(id) ON DELETE SET NULL,
        FOREIGN KEY (major_id) REFERENCES majors(id) ON DELETE SET NULL,
        FOREIGN KEY (dorm_building_id) REFERENCES dorm_buildings(id) ON DELETE SET NULL
      )
    `);
    console.log('✅ 用户表创建成功');

    // 创建维修请求表
    await connection.execute(`
      CREATE TABLE IF NOT EXISTS repair_requests (
        id VARCHAR(50) PRIMARY KEY,
        student_id VARCHAR(50) NOT NULL,
        room_id VARCHAR(50),
        room_number VARCHAR(20),
        dorm_building_id VARCHAR(50),
        description TEXT NOT NULL,
        status ENUM('待处理', '已指派', '维修中', '已完成', '已确认', '已取消') DEFAULT '待处理',
        assigned_staff_id VARCHAR(50),
        notes TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (dorm_building_id) REFERENCES dorm_buildings(id) ON DELETE SET NULL,
        FOREIGN KEY (assigned_staff_id) REFERENCES users(id) ON DELETE SET NULL
      )
    `);
    console.log('✅ 维修请求表创建成功');

    // 插入测试数据
    console.log('🔄 插入测试数据...');

    // 插入学院数据
    await connection.execute(`
      INSERT IGNORE INTO colleges (id, name) VALUES
      ('college01', '计算机科学学院'),
      ('college02', '电子工程学院'),
      ('college03', '机械工程学院'),
      ('college04', '经济管理学院')
    `);

    // 插入专业数据
    await connection.execute(`
      INSERT IGNORE INTO majors (id, name, college_id) VALUES
      ('major01', '计算机科学与技术', 'college01'),
      ('major02', '软件工程', 'college01'),
      ('major03', '电子信息工程', 'college02'),
      ('major04', '通信工程', 'college02')
    `);

    // 插入宿舍楼数据
    await connection.execute(`
      INSERT IGNORE INTO dorm_buildings (id, name, floors, total_rooms) VALUES
      ('bldgA', 'A栋', 6, 120),
      ('bldgB', 'B栋', 8, 160),
      ('bldgC', 'C栋', 5, 100)
    `);

    // 插入用户数据（使用bcrypt加密的密码：password123）
    await connection.execute(`
      INSERT IGNORE INTO users (id, name, email, password, role, phone) VALUES
      ('sysadmin01', '系统管理员', '<EMAIL>', '$2b$10$rOvHPGkwYvYFQVlXiXHxHOuBJKxj8qMxqGjZvQjZvQjZvQjZvQjZvO', '系统管理员', '13800000001'),
      ('repair01', '爱德华·修理工', '<EMAIL>', '$2b$10$rOvHPGkwYvYFQVlXiXHxHOuBJKxj8qMxqGjZvQjZvQjZvQjZvQjZvO', '维修人员', '13800000005'),
      ('dormadmin01', '张三', '<EMAIL>', '$2b$10$rOvHPGkwYvYFQVlXiXHxHOuBJKxj8qMxqGjZvQjZvQjZvQjZvQjZvO', '宿舍管理员', '13800000002'),
      ('dormadmin02', '李四', '<EMAIL>', '$2b$10$rOvHPGkwYvYFQVlXiXHxHOuBJKxj8qMxqGjZvQjZvQjZvQjZvQjZvO', '宿舍管理员', '13800000003'),
      ('student01', '王五', '<EMAIL>', '$2b$10$rOvHPGkwYvYFQVlXiXHxHOuBJKxj8qMxqGjZvQjZvQjZvQjZvQjZvO', '学生', '13800000004'),
      ('student02', '赵六', '<EMAIL>', '$2b$10$rOvHPGkwYvYFQVlXiXHxHOuBJKxj8qMxqGjZvQjZvQjZvQjZvQjZvO', '学生', '13800000006'),
      ('student03', '孙七', '<EMAIL>', '$2b$10$rOvHPGkwYvYFQVlXiXHxHOuBJKxj8qMxqGjZvQjZvQjZvQjZvQjZvO', '学生', '13800000007')
    `);

    // 插入维修请求数据
    await connection.execute(`
      INSERT IGNORE INTO repair_requests (id, student_id, room_number, dorm_building_id, description, status, assigned_staff_id, notes) VALUES
      ('repair001', 'student01', '101', 'bldgA', '水龙头漏水', '待处理', NULL, NULL),
      ('repair002', 'student02', '205', 'bldgB', '灯泡坏了', '已指派', 'repair01', '已安排维修人员'),
      ('repair003', 'student03', '102', 'bldgA', '门锁损坏', '已完成', 'repair01', '已更换新门锁')
    `);

    console.log('✅ 测试数据插入成功');
    console.log('🎉 数据库初始化完成！');
    
  } catch (error) {
    console.error('❌ 数据库初始化失败:', error.message);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
};

initDatabase();
