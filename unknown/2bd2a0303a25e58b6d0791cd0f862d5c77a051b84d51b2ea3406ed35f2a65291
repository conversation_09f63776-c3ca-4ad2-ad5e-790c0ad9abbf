-- 快速设置数据库
CREATE DATABASE IF NOT EXISTS dorm_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE dorm_management;

-- 删除现有表
DROP TABLE IF EXISTS repair_requests;
DROP TABLE IF EXISTS users;
DROP TABLE IF EXISTS majors;
DROP TABLE IF EXISTS colleges;
DROP TABLE IF EXISTS dorm_buildings;
DROP TABLE IF EXISTS user_roles;

-- 创建用户角色表
CREATE TABLE user_roles (
  id INT PRIMARY KEY AUTO_INCREMENT,
  role_name VARCHAR(50) NOT NULL UNIQUE,
  role_description TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建学院表
CREATE TABLE colleges (
  id VARCHAR(50) PRIMARY KEY,
  name VARCHAR(100) NOT NULL UNIQUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建专业表
CREATE TABLE majors (
  id VARCHAR(50) PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  college_id VARCHAR(50) NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (college_id) REFERENCES colleges(id)
);

-- 创建宿舍楼表
CREATE TABLE dorm_buildings (
  id VARCHAR(50) PRIMARY KEY,
  name VARCHAR(100) NOT NULL UNIQUE,
  floors INT NOT NULL,
  total_rooms INT NOT NULL,
  assigned_admin_id VARCHAR(50),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建用户表
CREATE TABLE users (
  id VARCHAR(50) PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  email VARCHAR(150) NOT NULL UNIQUE,
  password VARCHAR(255) NOT NULL,
  phone VARCHAR(20),
  role_id INT NOT NULL,
  college_id VARCHAR(50),
  major_id VARCHAR(50),
  dorm_building_id VARCHAR(50),
  room_number VARCHAR(20),
  emergency_contact_name VARCHAR(100),
  emergency_contact_phone VARCHAR(20),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (role_id) REFERENCES user_roles(id),
  FOREIGN KEY (college_id) REFERENCES colleges(id),
  FOREIGN KEY (major_id) REFERENCES majors(id),
  FOREIGN KEY (dorm_building_id) REFERENCES dorm_buildings(id)
);

-- 创建维修请求表
CREATE TABLE repair_requests (
  id VARCHAR(50) PRIMARY KEY,
  student_id VARCHAR(50) NOT NULL,
  room_number VARCHAR(20),
  dorm_building_id VARCHAR(50),
  description TEXT NOT NULL,
  status ENUM('待处理', '已指派', '维修中', '已完成', '已确认', '已取消') DEFAULT '待处理',
  assigned_staff_id VARCHAR(50),
  notes TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (student_id) REFERENCES users(id),
  FOREIGN KEY (dorm_building_id) REFERENCES dorm_buildings(id),
  FOREIGN KEY (assigned_staff_id) REFERENCES users(id)
);

-- 插入角色数据
INSERT INTO user_roles (id, role_name, role_description) VALUES
(1, '系统管理员', '系统管理员，拥有所有权限'),
(2, '宿舍管理员', '宿舍管理员，管理特定宿舍楼'),
(3, '学生', '学生用户'),
(4, '维修人员', '维修人员，处理维修请求');

-- 插入学院数据
INSERT INTO colleges (id, name) VALUES
('college01', '计算机科学学院'),
('college02', '电子工程学院'),
('college03', '机械工程学院'),
('college04', '经济管理学院');

-- 插入专业数据
INSERT INTO majors (id, name, college_id) VALUES
('major01', '计算机科学与技术', 'college01'),
('major02', '软件工程', 'college01'),
('major03', '电子信息工程', 'college02'),
('major04', '通信工程', 'college02');

-- 插入宿舍楼数据
INSERT INTO dorm_buildings (id, name, floors, total_rooms) VALUES
('bldgA', 'A栋', 6, 120),
('bldgB', 'B栋', 8, 160),
('bldgC', 'C栋', 5, 100);

-- 插入用户数据（密码是 password123 的bcrypt哈希）
INSERT INTO users (id, name, email, password, role_id, phone, college_id, major_id, dorm_building_id, emergency_contact_name, emergency_contact_phone) VALUES
('sysadmin01', '系统管理员', '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 1, '13800000001', NULL, NULL, NULL, NULL, NULL),
('repair01', '爱德华·修理工', '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 4, '13800000005', NULL, NULL, NULL, NULL, NULL),
('dormadmin01', '张三', '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 2, '13800000002', NULL, NULL, 'bldgA', NULL, NULL),
('dormadmin02', '李四', '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 2, '13800000003', NULL, NULL, 'bldgB', NULL, NULL),
('student01', '王五', '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 3, '13800000004', 'college01', 'major01', 'bldgA', '王父', '13900000001'),
('student02', '赵六', '<EMAIL>', '$2b$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 3, '13800000006', 'college02', 'major03', 'bldgB', '赵母', '13900000002');

-- 插入维修请求数据
INSERT INTO repair_requests (id, student_id, room_number, dorm_building_id, description, status, assigned_staff_id, notes) VALUES
('repair001', 'student01', '101', 'bldgA', '水龙头漏水', '待处理', NULL, NULL),
('repair002', 'student02', '205', 'bldgB', '灯泡坏了', '已指派', 'repair01', '已安排维修人员');
