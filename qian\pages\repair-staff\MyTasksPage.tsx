import React, { useState, FormEvent, useEffect } from 'react';
import { RepairRequest, RepairStatus, UserRole } from '../../types';
import { useAuth } from '../../contexts/AuthContext';
import Table from '../../components/Table';
import Button from '../../components/Button';
import Modal from '../../components/Modal';
import Card from '../../components/Card';
import Input from '../../components/Input'; // For notes in update modal

const API_BASE_URL = 'http://localhost:3001/api';

const MyTasksPage: React.FC = () => {
  const { currentUser } = useAuth();
  const [repairs, setRepairs] = useState<RepairRequest[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  const [currentTaskDetail, setCurrentTaskDetail] = useState<RepairRequest | null>(null);

  const [isUpdateStatusModalOpen, setIsUpdateStatusModalOpen] = useState(false);
  const [updatingTask, setUpdatingTask] = useState<RepairRequest | null>(null);
  const [newStatus, setNewStatus] = useState<RepairStatus | ''>('');
  const [updateNotes, setUpdateNotes] = useState('');

  // 获取维修请求列表
  const fetchRepairs = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`${API_BASE_URL}/repairs`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('获取维修请求失败');
      }

      const data = await response.json();
      if (data.success) {
        setRepairs(data.data.repairRequests || []);
      }
    } catch (error) {
      console.error('获取维修请求错误:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // 组件加载时获取数据
  useEffect(() => {
    if (currentUser) {
      fetchRepairs();
    }
  }, [currentUser]);

  if (!currentUser || currentUser.role !== UserRole.REPAIR_STAFF) {
    return <p>权限不足或用户信息加载失败。</p>;
  }

  const assignedTasks = repairs.filter(r => 
    r.assignedTo === currentUser.id && 
    (r.status === RepairStatus.ASSIGNED || r.status === RepairStatus.IN_PROGRESS || r.status === RepairStatus.COMPLETED)
  ).sort((a,b) => new Date(b.submittedAt).getTime() - new Date(a.submittedAt).getTime());

  const getStatusColor = (status: RepairStatus) => {
    switch(status) {
        case RepairStatus.PENDING: return 'bg-red-100 text-red-700';
        case RepairStatus.ASSIGNED: return 'bg-yellow-100 text-yellow-700';
        case RepairStatus.IN_PROGRESS: return 'bg-blue-100 text-blue-700';
        case RepairStatus.COMPLETED: return 'bg-green-100 text-green-700';
        case RepairStatus.CONFIRMED: return 'bg-purple-100 text-purple-700';
        default: return 'bg-gray-100 text-gray-700';
    }
  };

  const columns = [
    { header: 'ID', accessor: 'id' as keyof RepairRequest, className: 'w-1/12 truncate' },
    { header: '问题描述', accessor: 'description' as keyof RepairRequest, render: (item: RepairRequest) => <span title={item.description}>{item.description.substring(0,30)}...</span> },
    { header: '房间号', accessor: 'roomNumber' as keyof RepairRequest },
    { header: '楼栋', accessor: 'dormBuilding' as keyof RepairRequest },
    { header: '状态', accessor: 'status' as keyof RepairRequest, render: (item: RepairRequest) => <span className={`px-2 py-1 text-xs font-semibold rounded-full ${ getStatusColor(item.status) }`}>{item.status}</span> },
    { header: '提交人', accessor: 'studentName' as keyof RepairRequest },
    { 
      header: '操作', 
      accessor: 'id' as keyof RepairRequest, 
      render: (task: RepairRequest) => (
        <div className="space-x-1 whitespace-nowrap">
          <Button size="sm" variant="ghost" onClick={() => handleViewDetails(task)}><i className="fas fa-eye"></i></Button>
          {(task.status === RepairStatus.ASSIGNED || task.status === RepairStatus.IN_PROGRESS) && (
            <Button size="sm" variant="secondary" onClick={() => handleOpenUpdateStatusModal(task)}><i className="fas fa-edit"></i> 更新</Button>
          )}
        </div>
      )
    }
  ];
  
  const handleViewDetails = (task: RepairRequest) => {
    setCurrentTaskDetail(task);
    setIsDetailModalOpen(true);
  };

  const handleOpenUpdateStatusModal = (task: RepairRequest) => {
    setUpdatingTask(task);
    setNewStatus(task.status); // Pre-fill current status or allow selection
    setUpdateNotes('');
    setIsUpdateStatusModalOpen(true);
  };

  const handleUpdateTaskStatus = async (e: FormEvent) => {
    e.preventDefault();
    if (updatingTask && newStatus) {
      try {
        const token = localStorage.getItem('token');
        
        const response = await fetch(`${API_BASE_URL}/repairs/${updatingTask.id}/status`, {
          method: 'PUT',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            status: newStatus,
            notes: updateNotes || `状态已更新为 ${newStatus}`
          }),
        });

        if (!response.ok) {
          throw new Error('更新任务状态失败');
        }

        const data = await response.json();
        if (data.success) {
          // 重新获取维修请求列表
          await fetchRepairs();
          setIsUpdateStatusModalOpen(false);
          setUpdatingTask(null);
          setNewStatus('');
          setUpdateNotes('');
        }
      } catch (error) {
        console.error('更新任务状态错误:', error);
        alert('更新任务状态失败，请重试');
      }
    }
  };

  if (isLoading) {
    return (
      <Card title="我的维修任务">
        <div className="flex justify-center items-center py-8">
          <div className="text-gray-500">加载中...</div>
        </div>
      </Card>
    );
  }

  return (
    <Card title="我的已指派任务">
      <Table columns={columns} data={assignedTasks} keyExtractor={(r) => r.id} emptyStateMessage="您当前没有已指派的任务。" />

      {/* View Details Modal */}
      {currentTaskDetail && (
        <Modal isOpen={isDetailModalOpen} onClose={() => setIsDetailModalOpen(false)} title={`报修详情 - ${currentTaskDetail.id}`} size="lg">
            <div className="space-y-3">
                <p><strong>问题描述:</strong> {currentTaskDetail.description}</p>
                <p><strong>房间:</strong> {currentTaskDetail.roomNumber}, <strong>楼栋:</strong> {currentTaskDetail.dormBuilding}</p>
                <p><strong>状态:</strong> <span className={`px-2 py-1 text-xs font-semibold rounded-full ${ getStatusColor(currentTaskDetail.status) }`}>{currentTaskDetail.status}</span></p>
                <p><strong>提交人:</strong> {currentTaskDetail.studentName} ({currentTaskDetail.contact})</p>
                <p><strong>提交时间:</strong> {new Date(currentTaskDetail.submittedAt).toLocaleString()}</p>
                <p><strong>指派给:</strong> {currentTaskDetail.assignedToName || '未指派'}</p>
                {currentTaskDetail.imageUrl && (
                    <div className="my-2">
                        <strong>图片:</strong> 
                        <img src={currentTaskDetail.imageUrl} alt="报修图片" className="max-w-md max-h-md rounded mt-1 border"/>
                    </div>
                )}
                {currentTaskDetail.updates && currentTaskDetail.updates.length > 0 && (
                    <div>
                        <h4 className="font-semibold mt-2 border-t pt-2">更新记录:</h4>
                        <ul className="list-disc list-inside text-sm space-y-1 max-h-40 overflow-y-auto">
                            {currentTaskDetail.updates.map((update, index) => (
                                <li key={index}>
                                    {new Date(update.timestamp).toLocaleString()}: 由 <strong>{update.updatedBy}</strong> - {update.notes} 
                                    {update.newStatus && ` (状态改为 ${update.newStatus})`}
                                </li>
                            ))}
                        </ul>
                    </div>
                )}
            </div>
             <div className="mt-6 flex justify-end">
                <Button onClick={() => setIsDetailModalOpen(false)}>关闭</Button>
            </div>
        </Modal>
      )}

      {/* Update Status Modal */}
      {updatingTask && (
        <Modal isOpen={isUpdateStatusModalOpen} onClose={() => setIsUpdateStatusModalOpen(false)} title={`更新报修状态 - ${updatingTask.id}`}>
            <form onSubmit={handleUpdateTaskStatus} className="space-y-4">
                <p><strong>当前状态:</strong> {updatingTask.status}</p>
                <div>
                    <label htmlFor="newStatus" className="block text-sm font-medium text-gray-700 mb-1">新状态</label>
                    <select 
                        id="newStatus" 
                        value={newStatus} 
                        onChange={(e) => setNewStatus(e.target.value as RepairStatus)}
                        className="block w-full px-3 py-2 border border-neutral-DEFAULT rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
                        required
                    >
                        <option value="" disabled>-- 选择状态 --</option>
                        <option value={RepairStatus.IN_PROGRESS}>维修中</option>
                        <option value={RepairStatus.COMPLETED}>已完成 (待学生确认)</option>
                        {/* Add other relevant statuses if needed, e.g., CANCELLED, ON_HOLD */}
                    </select>
                </div>
                <div>
                    <label htmlFor="updateNotes" className="block text-sm font-medium text-gray-700 mb-1">备注 (可选)</label>
                    <textarea 
                        id="updateNotes" 
                        value={updateNotes} 
                        onChange={(e) => setUpdateNotes(e.target.value)}
                        rows={3}
                        placeholder="例如：已更换零件，预计明日完成..."
                        className="block w-full px-3 py-2 border border-neutral-DEFAULT rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
                    />
                </div>
                <div className="flex justify-end space-x-2 pt-4">
                    <Button type="button" variant="ghost" onClick={() => setIsUpdateStatusModalOpen(false)}>取消</Button>
                    <Button type="submit" disabled={!newStatus}>更新状态</Button>
                </div>
            </form>
        </Modal>
      )}
    </Card>
  );
};

export default MyTasksPage;