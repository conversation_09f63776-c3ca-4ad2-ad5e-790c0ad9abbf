// 测试API的简单脚本
const testLogin = async () => {
  try {
    const response = await fetch('http://localhost:3002/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password123',
        role: '系统管理员'
      }),
    });

    const data = await response.json();
    console.log('登录测试结果:', data);

    if (data.success) {
      console.log('✅ 登录成功！');
      console.log('用户信息:', data.data.user);
      console.log('令牌:', data.data.token);
    } else {
      console.log('❌ 登录失败:', data.message);
    }
  } catch (error) {
    console.error('❌ 请求失败:', error.message);
  }
};

testLogin();
