import { pool } from './config/database.js';

async function debugZhangSan() {
  try {
    console.log('🔍 调试张三的学生分配问题...');
    
    // 1. 检查张三的账户信息
    console.log('\n👤 检查张三账户信息:');
    const [zhang<PERSON>] = await pool.execute('SELECT * FROM users WHERE email = "<EMAIL>"');
    if (zhangsan.length > 0) {
      const user = zhangsan[0];
      console.log('张三信息:', {
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role_name,
        building_id: user.dorm_building_id
      });
    } else {
      console.log('❌ 找不到张三的账户');
      return;
    }
    
    // 2. 检查宿舍楼信息
    console.log('\n🏢 检查宿舍楼信息:');
    const [buildings] = await pool.execute('SELECT * FROM dorm_buildings');
    buildings.forEach(b => {
      console.log(`宿舍楼: ${b.name} (${b.id}) - 管理员: ${b.assigned_admin_id}`);
    });
    
    // 3. 检查所有学生
    console.log('\n👥 检查所有学生:');
    const [allStudents] = await pool.execute('SELECT id, name, role_name, dorm_building_id, room_number FROM users WHERE role_name = "学生"');
    console.log(`学生总数: ${allStudents.length}`);
    allStudents.forEach(s => {
      console.log(`  - ${s.name} (${s.id}) - 宿舍楼: ${s.dorm_building_id || '未分配'} - 房间: ${s.room_number || '未分配'}`);
    });
    
    // 4. 检查房间和床位
    console.log('\n🏠 检查房间和床位:');
    const [rooms] = await pool.execute('SELECT * FROM rooms WHERE dorm_building_id = "bldgA"');
    console.log(`A栋房间数: ${rooms.length}`);
    
    const [beds] = await pool.execute(`
      SELECT b.*, r.room_number, r.dorm_building_id 
      FROM beds b 
      JOIN rooms r ON b.room_id = r.id 
      WHERE r.dorm_building_id = "bldgA"
    `);
    console.log(`A栋床位数: ${beds.length}`);
    const occupiedBeds = beds.filter(b => b.status === '已入住').length;
    console.log(`已入住: ${occupiedBeds}, 空闲: ${beds.length - occupiedBeds}`);
    
    // 5. 模拟API调用
    console.log('\n🔧 模拟学生分配API调用:');
    
    // 模拟获取学生列表
    const [students] = await pool.execute(`
      SELECT 
        u.id, u.name, u.email, u.phone, u.room_number, u.dorm_building_id,
        u.emergency_contact_name, u.emergency_contact_phone,
        db.name as building_name,
        b.bed_number, b.status as bed_status
      FROM users u
      LEFT JOIN dorm_buildings db ON u.dorm_building_id = db.id
      LEFT JOIN beds b ON b.student_id = u.id
      WHERE u.role_name = '学生' AND (u.dorm_building_id = 'bldgA' OR u.dorm_building_id IS NULL)
      ORDER BY u.name
    `);
    
    console.log(`API查询结果: ${students.length}个学生`);
    students.forEach(s => {
      console.log(`  - ${s.name} (${s.id}) - 宿舍楼: ${s.dorm_building_id || '未分配'} - 房间: ${s.room_number || '未分配'} - 床位: ${s.bed_number || '未分配'}`);
    });
    
    // 6. 模拟获取空闲床位
    const [vacantBeds] = await pool.execute(`
      SELECT 
        b.id, b.bed_number, b.status,
        r.id as room_id, r.room_number, r.floor, r.type, r.capacity
      FROM beds b
      JOIN rooms r ON b.room_id = r.id
      WHERE r.dorm_building_id = 'bldgA' AND b.status = '空闲'
      ORDER BY r.floor, r.room_number, b.bed_number
    `);
    
    console.log(`空闲床位: ${vacantBeds.length}个`);
    vacantBeds.slice(0, 5).forEach(b => {
      console.log(`  - 房间${b.room_number} 床位${b.bed_number}`);
    });
    
    await pool.end();
    console.log('\n🎉 调试完成！');
  } catch (error) {
    console.error('❌ 调试失败:', error);
    process.exit(1);
  }
}

debugZhangSan();
