import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

dotenv.config();

const checkData = async () => {
  let connection;
  
  try {
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || 'root',
      database: process.env.DB_NAME || 'dorm_management',
      charset: 'utf8mb4'
    });

    console.log('✅ 连接到数据库成功');

    // 检查用户数据
    console.log('\n📋 用户数据:');
    const [users] = await connection.execute(`
      SELECT u.id, u.name, u.email, ur.role_name, u.phone 
      FROM users u 
      LEFT JOIN user_roles ur ON u.role_id = ur.id 
      ORDER BY u.created_at DESC
    `);
    
    if (users.length === 0) {
      console.log('   ⚠️ 没有用户数据');
    } else {
      users.forEach(user => {
        console.log(`   - ${user.name} (${user.email}) - 角色: ${user.role_name || '未分配'}`);
      });
    }

    // 检查学院数据
    console.log('\n📋 学院数据:');
    const [colleges] = await connection.execute('SELECT * FROM colleges ORDER BY created_at DESC');
    
    if (colleges.length === 0) {
      console.log('   ⚠️ 没有学院数据');
    } else {
      colleges.forEach(college => {
        console.log(`   - ${college.name} (ID: ${college.id})`);
      });
    }

    // 检查专业数据
    console.log('\n📋 专业数据:');
    const [majors] = await connection.execute(`
      SELECT m.name, c.name as college_name 
      FROM majors m 
      LEFT JOIN colleges c ON m.college_id = c.id 
      ORDER BY m.created_at DESC
    `);
    
    if (majors.length === 0) {
      console.log('   ⚠️ 没有专业数据');
    } else {
      majors.forEach(major => {
        console.log(`   - ${major.name} (所属学院: ${major.college_name || '未知'})`);
      });
    }

    // 检查维修请求数据
    console.log('\n📋 维修请求数据:');
    const [repairs] = await connection.execute(`
      SELECT rr.id, rr.description, rr.status, u.name as student_name, rr.created_at
      FROM repair_requests rr 
      LEFT JOIN users u ON rr.student_id = u.id 
      ORDER BY rr.created_at DESC
    `);
    
    if (repairs.length === 0) {
      console.log('   ⚠️ 没有维修请求数据');
    } else {
      repairs.forEach(repair => {
        console.log(`   - ${repair.description} (状态: ${repair.status}, 提交人: ${repair.student_name || '未知'}, 时间: ${repair.created_at})`);
      });
    }

    // 检查角色数据
    console.log('\n📋 角色数据:');
    const [roles] = await connection.execute('SELECT * FROM user_roles ORDER BY id');
    
    if (roles.length === 0) {
      console.log('   ⚠️ 没有角色数据');
    } else {
      roles.forEach(role => {
        console.log(`   - ID: ${role.id}, 名称: ${role.role_name}, 描述: ${role.role_description || '无'}`);
      });
    }

    console.log('\n📊 数据统计:');
    console.log(`   - 用户总数: ${users.length}`);
    console.log(`   - 学院总数: ${colleges.length}`);
    console.log(`   - 专业总数: ${majors.length}`);
    console.log(`   - 维修请求总数: ${repairs.length}`);
    console.log(`   - 角色总数: ${roles.length}`);
    
  } catch (error) {
    console.error('❌ 检查数据失败:', error.message);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
};

checkData();
