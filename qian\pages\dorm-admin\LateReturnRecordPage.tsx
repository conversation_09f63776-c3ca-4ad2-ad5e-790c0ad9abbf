
import React, { useState, useEffect, FormEvent } from 'react';
import { LateReturn, UserRole } from '../../types';
import { MOCK_USERS, getBuildingIdByName } from '../../constants';
import { useAuth } from '../../contexts/AuthContext';
import Table from '../../components/Table';
import Card from '../../components/Card';
import Button from '../../components/Button';
import Modal from '../../components/Modal';
import Input from '../../components/Input';

const LateReturnRecordPage: React.FC = () => {
  const { currentUser } = useAuth();
  const [lateReturns, setLateReturns] = useState<LateReturn[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [newLateReturn, setNewLateReturn] = useState<Partial<LateReturn>>({});
  const [loading, setLoading] = useState(true);
  const [students, setStudents] = useState<any[]>([]);

  if (!currentUser || currentUser.role !== UserRole.DORM_ADMIN) return <p>权限不足。</p>;

  // 直接使用宿舍楼ID，因为张三管理A栋
  const adminBuildingId = currentUser.id === 'dormadmin01' ? 'bldgA' :
                         currentUser.id === 'dormadmin02' ? 'bldgB' :
                         getBuildingIdByName(currentUser.dormBuilding);
  const buildingName = currentUser.id === 'dormadmin01' ? 'A栋' :
                      currentUser.id === 'dormadmin02' ? 'B栋' :
                      currentUser.dormBuilding || "未知楼栋";

  // 获取晚归记录数据
  const fetchLateReturns = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('token');
      const response = await fetch(`http://localhost:3002/api/late-returns?building_id=${adminBuildingId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          const formattedData = data.data.map((lr: any) => ({
            id: lr.id,
            studentId: lr.student_id,
            studentName: lr.student_name,
            dormBuildingId: lr.dorm_building_id,
            date: lr.date,
            time: lr.time,
            reason: lr.reason,
            recordedBy: lr.recorded_by
          }));
          setLateReturns(formattedData);
        }
      } else {
        console.error('获取晚归记录失败');
      }
    } catch (error) {
      console.error('获取晚归记录错误:', error);
    } finally {
      setLoading(false);
    }
  };

  // 获取学生列表
  const fetchStudents = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`http://localhost:3002/api/student-allocation/students?building_id=${adminBuildingId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setStudents(data.data);
        }
      }
    } catch (error) {
      console.error('获取学生列表错误:', error);
    }
  };

  useEffect(() => {
    fetchLateReturns();
    fetchStudents();
  }, [adminBuildingId]);

  const columns = [
    { header: '学生姓名', accessor: 'studentName' as keyof LateReturn },
    { header: '日期', accessor: 'date' as keyof LateReturn },
    { header: '时间', accessor: 'time' as keyof LateReturn },
    { header: '原因', accessor: 'reason' as keyof LateReturn },
    { 
      header: '操作', 
      accessor: 'id' as keyof LateReturn,
      render: (record: LateReturn) => (
        <Button size="sm" variant="danger" onClick={() => handleDelete(record.id)}><i className="fas fa-trash"></i></Button>
      )
    }
  ];

  const handleOpenModal = () => {
    setNewLateReturn({ recordedBy: currentUser.id, dormBuildingId: adminBuildingId, date: new Date().toISOString().split('T')[0], time: "23:00" });
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setNewLateReturn({});
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
     if (name === "studentId") {
        const student = students.find(s => s.id === value);
        setNewLateReturn(prev => ({ ...prev, studentId: value, studentName: student?.name }));
    } else {
        setNewLateReturn(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (newLateReturn.studentId && newLateReturn.date && newLateReturn.time && newLateReturn.reason) {
      try {
        const token = localStorage.getItem('token');
        const response = await fetch('http://localhost:3002/api/late-returns', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            student_id: newLateReturn.studentId,
            dorm_building_id: adminBuildingId,
            date: newLateReturn.date,
            time: newLateReturn.time,
            reason: newLateReturn.reason
          })
        });

        if (response.ok) {
          const data = await response.json();
          if (data.success) {
            alert('晚归记录创建成功');
            handleCloseModal();
            fetchLateReturns(); // 重新获取数据
          }
        } else {
          alert('创建晚归记录失败');
        }
      } catch (error) {
        console.error('创建晚归记录错误:', error);
        alert('创建晚归记录失败');
      }
    } else {
      alert("请填写所有必填项。");
    }
  };

  const handleDelete = async (id: string) => {
    if (window.confirm("确定删除此晚归记录吗?")) {
      try {
        const token = localStorage.getItem('token');
        const response = await fetch(`http://localhost:3002/api/late-returns/${id}`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        if (response.ok) {
          const data = await response.json();
          if (data.success) {
            alert('晚归记录删除成功');
            fetchLateReturns(); // 重新获取数据
          }
        } else {
          alert('删除晚归记录失败');
        }
      } catch (error) {
        console.error('删除晚归记录错误:', error);
        alert('删除晚归记录失败');
      }
    }
  }

  if (loading) {
    return <div className="flex justify-center items-center h-64">加载中...</div>;
  }

  return (
    <Card title={`${buildingName} - 晚归记录管理`} actions={<Button onClick={handleOpenModal} leftIcon={<i className="fas fa-user-clock mr-2"></i>}>登记晚归</Button>}>
      <Table columns={columns} data={lateReturns} keyExtractor={lr => lr.id} />
      <Modal isOpen={isModalOpen} onClose={handleCloseModal} title="登记晚归">
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="studentId" className="block text-sm font-medium text-gray-700 mb-1">学生</label>
            <select
              id="studentId"
              name="studentId"
              value={newLateReturn.studentId || ''}
              onChange={handleInputChange}
              className="block w-full px-3 py-2 border border-neutral-DEFAULT rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm"
              required
            >
              <option value="">-- 选择学生 --</option>
              {students.map(s => <option key={s.id} value={s.id}>{s.name} ({s.room_number || '未分配'})</option>)}
            </select>
          </div>
          <Input name="date" label="日期" type="date" value={newLateReturn.date || ''} onChange={handleInputChange} required />
          <Input name="time" label="时间" type="time" value={newLateReturn.time || ''} onChange={handleInputChange} required />
          <Input name="reason" label="原因" value={newLateReturn.reason || ''} onChange={handleInputChange} required />
          <div className="flex justify-end space-x-2 pt-4">
            <Button type="button" variant="ghost" onClick={handleCloseModal}>取消</Button>
            <Button type="submit">保存记录</Button>
          </div>
        </form>
      </Modal>
    </Card>
  );
};

export default LateReturnRecordPage;
