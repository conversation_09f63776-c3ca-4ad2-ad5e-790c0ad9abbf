import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import { pool } from '../config/database.js';

// 生成JWT令牌
const generateToken = (userId) => {
  return jwt.sign({ userId }, process.env.JWT_SECRET, {
    expiresIn: process.env.JWT_EXPIRES_IN || '24h'
  });
};

// 用户登录
export const login = async (req, res) => {
  try {
    const { email, password, role } = req.body;

    if (!email || !password || !role) {
      return res.status(400).json({
        success: false,
        message: '邮箱、密码和角色都是必填项'
      });
    }

    // 查找用户
    const [rows] = await pool.execute(
      `SELECT u.*, c.name as college_name, m.name as major_name, db.name as dorm_building_name, ur.role_name
       FROM users u
       LEFT JOIN colleges c ON u.college_id = c.id
       LEFT JOIN majors m ON u.major_id = m.id
       LEFT JOIN dorm_buildings db ON u.dorm_building_id = db.id
       LEFT JOIN user_roles ur ON u.role_id = ur.id
       WHERE u.email = ? AND ur.role_name = ?`,
      [email, role]
    );

    if (rows.length === 0) {
      return res.status(401).json({
        success: false,
        message: '邮箱、角色或密码不正确'
      });
    }

    const user = rows[0];

    // 验证密码
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        message: '邮箱、角色或密码不正确'
      });
    }

    // 生成JWT令牌
    const token = generateToken(user.id);

    // 返回用户信息（不包含密码）
    const userResponse = {
      id: user.id,
      name: user.name,
      email: user.email,
      role: user.role_name,
      phone: user.phone,
      college: user.college_name,
      major: user.major_name,
      dormBuilding: user.dorm_building_name,
      roomNumber: user.room_number,
      emergencyContactName: user.emergency_contact_name,
      emergencyContactPhone: user.emergency_contact_phone
    };

    res.json({
      success: true,
      message: '登录成功',
      data: {
        user: userResponse,
        token
      }
    });

  } catch (error) {
    console.error('登录错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};

// 用户注册
export const register = async (req, res) => {
  try {
    const { 
      name, 
      email, 
      password, 
      role, 
      phone, 
      college, 
      major, 
      dormBuilding, 
      roomNumber, 
      emergencyContactName, 
      emergencyContactPhone 
    } = req.body;

    if (!name || !email || !password || !role) {
      return res.status(400).json({
        success: false,
        message: '姓名、邮箱、密码和角色都是必填项'
      });
    }

    // 检查邮箱是否已存在
    const [existingUsers] = await pool.execute(
      'SELECT id FROM users WHERE email = ?',
      [email]
    );

    if (existingUsers.length > 0) {
      return res.status(400).json({
        success: false,
        message: '该邮箱已被注册'
      });
    }

    // 加密密码
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    // 生成用户ID
    const userId = `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // 插入新用户
    await pool.execute(
      `INSERT INTO users (
        id, name, email, password, role, phone, 
        room_number, emergency_contact_name, emergency_contact_phone
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        userId, name, email, hashedPassword, role, phone,
        roomNumber, emergencyContactName, emergencyContactPhone
      ]
    );

    // 生成JWT令牌
    const token = generateToken(userId);

    // 返回用户信息（不包含密码）
    const userResponse = {
      id: userId,
      name,
      email,
      role,
      phone,
      college,
      major,
      dormBuilding,
      roomNumber,
      emergencyContactName,
      emergencyContactPhone
    };

    res.status(201).json({
      success: true,
      message: '注册成功',
      data: {
        user: userResponse,
        token
      }
    });

  } catch (error) {
    console.error('注册错误:', error);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};
