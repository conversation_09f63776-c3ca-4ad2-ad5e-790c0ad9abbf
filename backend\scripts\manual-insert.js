import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

dotenv.config();

const manualInsert = async () => {
  let connection;
  
  try {
    console.log('🔄 手动插入测试数据...');
    
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || 'root',
      database: process.env.DB_NAME || 'dorm_management',
      charset: 'utf8mb4'
    });

    console.log('✅ 成功连接到数据库');

    // 插入学院数据
    console.log('🔄 插入学院数据...');
    try {
      await connection.execute(`
        INSERT INTO colleges (id, name) VALUES
        ('college01', '计算机科学学院'),
        ('college02', '电子工程学院'),
        ('college03', '机械工程学院'),
        ('college04', '经济管理学院')
        ON DUPLICATE KEY UPDATE name = VALUES(name)
      `);
      console.log('✅ 学院数据插入成功');
    } catch (error) {
      console.log('⚠️ 学院数据可能已存在:', error.message);
    }

    // 插入专业数据
    console.log('🔄 插入专业数据...');
    try {
      await connection.execute(`
        INSERT INTO majors (id, name, college_id) VALUES
        ('major01', '计算机科学与技术', 'college01'),
        ('major02', '软件工程', 'college01'),
        ('major03', '电子信息工程', 'college02'),
        ('major04', '通信工程', 'college02')
        ON DUPLICATE KEY UPDATE name = VALUES(name)
      `);
      console.log('✅ 专业数据插入成功');
    } catch (error) {
      console.log('⚠️ 专业数据插入失败:', error.message);
    }

    // 插入宿舍楼数据
    console.log('🔄 插入宿舍楼数据...');
    try {
      await connection.execute(`
        INSERT INTO dorm_buildings (id, name, floors, total_rooms) VALUES
        ('bldgA', 'A栋', 6, 120),
        ('bldgB', 'B栋', 8, 160),
        ('bldgC', 'C栋', 5, 100)
        ON DUPLICATE KEY UPDATE name = VALUES(name)
      `);
      console.log('✅ 宿舍楼数据插入成功');
    } catch (error) {
      console.log('⚠️ 宿舍楼数据插入失败:', error.message);
    }

    // 检查并插入角色数据
    console.log('🔄 检查角色数据...');
    const [roleCheck] = await connection.execute('SELECT COUNT(*) as count FROM user_roles WHERE role_name = ?', ['系统管理员']);
    
    if (roleCheck[0].count === 0) {
      await connection.execute(`
        INSERT INTO user_roles (id, role_name, role_description) VALUES
        (1, '系统管理员', '系统管理员，拥有所有权限'),
        (2, '宿舍管理员', '宿舍管理员，管理特定宿舍楼'),
        (3, '学生', '学生用户'),
        (4, '维修人员', '维修人员，处理维修请求')
        ON DUPLICATE KEY UPDATE role_name = VALUES(role_name)
      `);
      console.log('✅ 角色数据插入成功');
    } else {
      console.log('✅ 角色数据已存在');
    }

    // 插入用户数据
    console.log('🔄 插入用户数据...');
    try {
      await connection.execute(`
        INSERT INTO users (id, name, email, password, role_id, phone) VALUES
        ('sysadmin01', '系统管理员', '<EMAIL>', '$2b$10$rOvHPGkwYvYFQVlXiXHxHOuBJKxj8qMxqGjZvQjZvQjZvQjZvQjZvO', 1, '13800000001'),
        ('repair01', '爱德华·修理工', '<EMAIL>', '$2b$10$rOvHPGkwYvYFQVlXiXHxHOuBJKxj8qMxqGjZvQjZvQjZvQjZvQjZvO', 4, '13800000005'),
        ('dormadmin01', '张三', '<EMAIL>', '$2b$10$rOvHPGkwYvYFQVlXiXHxHOuBJKxj8qMxqGjZvQjZvQjZvQjZvQjZvO', 2, '13800000002'),
        ('dormadmin02', '李四', '<EMAIL>', '$2b$10$rOvHPGkwYvYFQVlXiXHxHOuBJKxj8qMxqGjZvQjZvQjZvQjZvQjZvO', 2, '13800000003'),
        ('student01', '王五', '<EMAIL>', '$2b$10$rOvHPGkwYvYFQVlXiXHxHOuBJKxj8qMxqGjZvQjZvQjZvQjZvQjZvO', 3, '13800000004'),
        ('student02', '赵六', '<EMAIL>', '$2b$10$rOvHPGkwYvYFQVlXiXHxHOuBJKxj8qMxqGjZvQjZvQjZvQjZvQjZvO', 3, '13800000006'),
        ('student03', '孙七', '<EMAIL>', '$2b$10$rOvHPGkwYvYFQVlXiXHxHOuBJKxj8qMxqGjZvQjZvQjZvQjZvQjZvO', 3, '13800000007')
        ON DUPLICATE KEY UPDATE name = VALUES(name)
      `);
      console.log('✅ 用户数据插入成功');
    } catch (error) {
      console.log('⚠️ 用户数据插入失败:', error.message);
    }

    // 验证数据
    console.log('🔄 验证数据...');
    const [userCount] = await connection.execute('SELECT COUNT(*) as count FROM users');
    const [collegeCount] = await connection.execute('SELECT COUNT(*) as count FROM colleges');
    const [roleCount] = await connection.execute('SELECT COUNT(*) as count FROM user_roles');

    console.log(`✅ 用户数量: ${userCount[0].count}`);
    console.log(`✅ 学院数量: ${collegeCount[0].count}`);
    console.log(`✅ 角色数量: ${roleCount[0].count}`);

    console.log('🎉 手动数据插入完成！');
    
  } catch (error) {
    console.error('❌ 数据插入失败:', error.message);
    console.error('错误详情:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
};

manualInsert();
