import mysql from 'mysql2/promise';
import bcrypt from 'bcrypt';

async function directAddStudents() {
  const connection = await mysql.createConnection({
    host: 'localhost',
    user: 'root',
    password: 'root',
    database: 'dorm_management'
  });

  try {
    console.log('👥 直接添加4个学生到A栋...');
    
    // 生成密码哈希
    const passwordHash = await bcrypt.hash('password123', 10);
    
    // 要添加的4个学生
    const students = [
      { id: 'student03', name: '孙七', email: '<EMAIL>' },
      { id: 'student04', name: '周八', email: '<EMAIL>' },
      { id: 'student05', name: '吴九', email: '<EMAIL>' },
      { id: 'student06', name: '郑十', email: '<EMAIL>' }
    ];
    
    console.log('\n📝 添加学生用户...');
    for (const student of students) {
      try {
        await connection.execute(`
          INSERT INTO users (id, name, email, password, role_id, phone, college_id, major_id, dorm_building_id, emergency_contact_name, emergency_contact_phone) 
          VALUES (?, ?, ?, ?, 3, ?, 'college01', 'major01', 'bldgA', ?, ?)
        `, [
          student.id, 
          student.name, 
          student.email, 
          passwordHash,
          '138' + String(Math.floor(Math.random() * 100000000)).padStart(8, '0'),
          student.name.slice(0, 1) + '父',
          '139' + String(Math.floor(Math.random() * 100000000)).padStart(8, '0')
        ]);
        console.log(`✅ 添加学生: ${student.name} -> A栋`);
      } catch (error) {
        if (error.code === 'ER_DUP_ENTRY') {
          console.log(`⚠️ 学生已存在: ${student.name}，更新宿舍楼信息...`);
          await connection.execute(`
            UPDATE users SET dorm_building_id = 'bldgA' WHERE id = ?
          `, [student.id]);
          console.log(`✅ 更新学生宿舍楼: ${student.name} -> A栋`);
        } else {
          console.log(`❌ 添加失败: ${student.name} - ${error.message}`);
        }
      }
    }
    
    // 检查A栋的学生情况
    console.log('\n📊 检查A栋学生情况:');
    const [buildingAStudents] = await connection.execute(`
      SELECT u.id, u.name, u.dorm_building_id, u.room_number
      FROM users u
      WHERE u.role_id = 3 AND u.dorm_building_id = 'bldgA'
      ORDER BY u.name
    `);
    
    console.log(`A栋学生总数: ${buildingAStudents.length}`);
    buildingAStudents.forEach(s => {
      console.log(`  - ${s.name} (${s.id}) - 房间: ${s.room_number || '未分配'}`);
    });
    
    await connection.end();
    console.log('\n🎉 学生数据添加完成！');
  } catch (error) {
    console.error('❌ 操作失败:', error);
    await connection.end();
    process.exit(1);
  }
}

directAddStudents();
